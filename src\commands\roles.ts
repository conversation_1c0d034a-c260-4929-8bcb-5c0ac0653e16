import { Slash<PERSON><PERSON>mandBuilder, ChatInputCommandInteraction } from 'discord.js';
import { RoleForSale } from '../models/User';
import User from '../models/User';
import { withErrorHand<PERSON>, DatabaseError } from '../utils/errorHandler';
import { createEconomyEmbed, formatCoins, createQuickActionButtons, EMOJIS, COLORS, addUserInfo } from '../utils/embedBuilder';
import { getUserAchievementRoles } from '../services/roleAssignmentService';

module.exports = {
  data: new SlashCommandBuilder()
    .setName('roles')
    .setDescription('Display all role achievements available to unlock'),
  execute: withError<PERSON>andler(async (interaction: ChatInputCommandInteraction) => {
    try {
      const roles = await RoleForSale.find().sort({ price: 1 }); // Sort by price ascending
      const discordId = interaction.user.id;

      if (!roles.length) {
        const embed = createEconomyEmbed('Role Achievements')
          .setDescription(`${EMOJIS.ROLES.MASK} No role achievements are currently available to unlock!\n\nCheck back later or ask an administrator to add some role achievements.`)
          .setColor(COLORS.INFO);

        await interaction.reply({
          embeds: [embed],
          ephemeral: true
        });
        return;
      }

      // Get user's current balance
      const user = await User.findOne({ discordId });
      const currentBalance = user?.balance || 0;

      // Get user's current achievement roles
      let userAchievements: Array<{roleId: string; roleName: string; price: number; description?: string}> = [];
      if (interaction.guild) {
        const member = await interaction.guild.members.fetch(discordId);
        userAchievements = await getUserAchievementRoles(member);
      }

      // Create rich embed with role information
      const embed = createEconomyEmbed('Phalanx Role Achievements')
        .setDescription(`${EMOJIS.ROLES.SHIELD} **Role Achievement Progress**\n\nRole achievements are automatically unlocked when you reach the required PLC balance! Your PLC balance represents your lifetime achievements and is never spent.`)
        .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png'); // You can add a custom shop icon

      // Add user's current balance
      embed.addFields({
        name: `${EMOJIS.ECONOMY.MONEY} Your Current Balance`,
        value: formatCoins(currentBalance),
        inline: true
      });

      // Add achieved roles section
      if (userAchievements.length > 0) {
        embed.addFields({
          name: `${EMOJIS.SUCCESS.CROWN} Unlocked Achievements (${userAchievements.length})`,
          value: userAchievements.map(achievement =>
            `${EMOJIS.SUCCESS.CHECK} **${achievement.roleName}** - ${formatCoins(achievement.price)}`
          ).join('\n'),
          inline: false
        });
      }

      // Add available roles section
      const availableRoles = roles.filter(role =>
        !userAchievements.some(achievement => achievement.roleId === role.roleId)
      );

      if (availableRoles.length > 0) {
        const roleFields = availableRoles.map(role => {
          const canUnlock = currentBalance >= role.price;
          const statusEmoji = canUnlock ? EMOJIS.SUCCESS.STAR : EMOJIS.MISC.CLOCK;
          const statusText = canUnlock ? '**Ready to unlock!**' : `Need ${formatCoins(role.price - currentBalance)} more`;

          return `${statusEmoji} **${role.name}** - ${formatCoins(role.price)}\n${statusText}`;
        });

        embed.addFields({
          name: `${EMOJIS.ROLES.MEDAL} Available Achievements (${availableRoles.length})`,
          value: roleFields.join('\n\n'),
          inline: false
        });
      }

      // Add summary information
      const totalRoles = roles.length;
      const unlockedCount = userAchievements.length;
      const progressPercentage = Math.round((unlockedCount / totalRoles) * 100);

      embed.addFields({
        name: `${EMOJIS.MISC.SCROLL} Achievement Progress`,
        value: `**${unlockedCount}/${totalRoles}** achievements unlocked (${progressPercentage}%)\n` +
               `${EMOJIS.ECONOMY.COINS} Your Balance: **${formatCoins(currentBalance)}** coins`,
        inline: false
      });

      embed.setFooter({
        text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
      });

      // Add user info to embed
      addUserInfo(embed, interaction.user);

      // Create quick action buttons
      const actionButtons = createQuickActionButtons();

      const components = [actionButtons];

      await interaction.reply({
        embeds: [embed],
        components: components,
        ephemeral: false
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new DatabaseError(error.message);
      } else {
        throw new DatabaseError('Failed to fetch roles.');
      }
    }
  })
};
