"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processStarterBalance = processStarterBalance;
exports.getStarterBalanceRules = getStarterBalanceRules;
exports.createStarterBalanceRule = createStarterBalanceRule;
exports.updateStarterBalanceRule = updateStarterBalanceRule;
exports.removeStarterBalanceRule = removeStarterBalanceRule;
exports.hasStarterBalanceRule = hasStarterBalanceRule;
const StarterBalance_1 = require("../models/StarterBalance");
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
/**
 * Processes starter balance when a user receives a role
 */
async function processStarterBalance(member, role) {
    try {
        // Check if there's a starter balance rule for this role
        const starterBalanceRule = await StarterBalance_1.StarterBalance.findOne({
            guildId: member.guild.id,
            roleId: role.id
        });
        if (!starterBalanceRule) {
            return false; // No starter balance rule for this role
        }
        // Grant the starter balance
        await (0, economyService_1.adjustBalance)(member.id, starterBalanceRule.amount, 'starter_balance', `Starter balance for role: ${role.name}`, member.client, member.guild.id);
        console.log(`[Starter Balance] Granted ${starterBalanceRule.amount} PLC to ${member.displayName} for receiving role ${role.name}`);
        // Send DM to user notifying them of the starter balance
        try {
            const embed = (0, embedBuilder_1.createSuccessEmbed)('Starter Balance Awarded!')
                .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Welcome Bonus!**\n\n` +
                `You have been awarded **${starterBalanceRule.amount} PLC** ` +
                `for receiving the **${role.name}** role!\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Use \`/balance\` to check your current balance and ` +
                `\`/roles\` to see what you can purchase with your coins.`)
                .setColor(embedBuilder_1.COLORS.SUCCESS)
                .setFooter({ text: 'Phalanx Order Economy System' });
            await member.send({ embeds: [embed] });
        }
        catch (dmError) {
            console.error(`[Starter Balance] Failed to send DM to ${member.displayName}:`, dmError);
            // Don't throw error for DM failure - the balance was still granted
        }
        return true;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to process starter balance for ${member.displayName} and role ${role.name}:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to process starter balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Gets all starter balance rules for a guild
 */
async function getStarterBalanceRules(guildId) {
    try {
        return await StarterBalance_1.StarterBalance.find({ guildId }).sort({ roleName: 1 });
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to get starter balance rules for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to retrieve starter balance rules: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Creates a new starter balance rule
 */
async function createStarterBalanceRule(guildId, roleId, roleName, amount) {
    try {
        // Check if rule already exists
        const existingRule = await StarterBalance_1.StarterBalance.findOne({ guildId, roleId });
        if (existingRule) {
            throw new errorHandler_1.DatabaseError(`Starter balance rule already exists for role ${roleName}`);
        }
        // Create new rule
        const newRule = new StarterBalance_1.StarterBalance({
            guildId,
            roleId,
            roleName,
            amount
        });
        await newRule.save();
        console.log(`[Starter Balance] Created new rule: ${amount} PLC for role ${roleName} in guild ${guildId}`);
        return newRule;
    }
    catch (error) {
        if (error instanceof errorHandler_1.DatabaseError) {
            throw error;
        }
        console.error(`[Starter Balance] Failed to create starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to create starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Updates an existing starter balance rule
 */
async function updateStarterBalanceRule(guildId, roleId, newAmount) {
    try {
        const updatedRule = await StarterBalance_1.StarterBalance.findOneAndUpdate({ guildId, roleId }, { amount: newAmount }, { new: true, runValidators: true });
        if (updatedRule) {
            console.log(`[Starter Balance] Updated rule for role ${updatedRule.roleName}: ${newAmount} PLC`);
        }
        return updatedRule;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to update starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to update starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Removes a starter balance rule
 */
async function removeStarterBalanceRule(guildId, roleId) {
    try {
        const deletedRule = await StarterBalance_1.StarterBalance.findOneAndDelete({ guildId, roleId });
        if (deletedRule) {
            console.log(`[Starter Balance] Removed rule for role ${deletedRule.roleName}`);
            return true;
        }
        return false;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to remove starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to remove starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Checks if a starter balance rule exists for a specific role
 */
async function hasStarterBalanceRule(guildId, roleId) {
    try {
        const rule = await StarterBalance_1.StarterBalance.findOne({ guildId, roleId });
        return !!rule;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to check starter balance rule existence:`, error);
        return false;
    }
}
