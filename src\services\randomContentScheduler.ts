import { Client } from 'discord.js';
import cron from 'node-cron';
import RandomContentConfig from '../models/RandomContentConfig';
import { postRandomContent } from './randomContentService';

interface ScheduledJob {
  guildId: string;
  task: cron.ScheduledTask;
  intervalMinutes: number;
}

class RandomContentScheduler {
  private client: Client;
  private scheduledJobs: Map<string, ScheduledJob> = new Map();
  private isInitialized: boolean = false;

  constructor(client: Client) {
    this.client = client;
  }

  /**
   * Initialize the scheduler and load all active configurations
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('[Random Content Scheduler] Already initialized');
      return;
    }

    try {
      console.log('[Random Content Scheduler] Initializing...');
      
      // Load all active configurations
      const activeConfigs = await RandomContentConfig.find({ enabled: true });
      
      for (const config of activeConfigs) {
        if (config.targetChannelId) {
          await this.scheduleGuild(config.guildId, config.postingIntervalMinutes, config.targetChannelId, config.tagFilter);
        }
      }

      this.isInitialized = true;
      console.log(`[Random Content Scheduler] Initialized with ${activeConfigs.length} active configurations`);
    } catch (error) {
      console.error('[Random Content Scheduler] Failed to initialize:', error);
    }
  }

  /**
   * Schedule random content posting for a guild
   */
  async scheduleGuild(
    guildId: string, 
    intervalMinutes: number, 
    channelId: string, 
    tagFilter?: string[]
  ): Promise<void> {
    try {
      // Remove existing job if any
      await this.unscheduleGuild(guildId);

      // Validate interval (minimum 5 minutes to prevent spam)
      if (intervalMinutes < 5) {
        console.warn(`[Random Content Scheduler] Invalid interval ${intervalMinutes} for guild ${guildId}, using minimum 5 minutes`);
        intervalMinutes = 5;
      }

      // Create cron expression for the interval
      const cronExpression = `*/${intervalMinutes} * * * *`;

      // Validate cron expression
      if (!cron.validate(cronExpression)) {
        console.error(`[Random Content Scheduler] Invalid cron expression: ${cronExpression}`);
        return;
      }

      // Create scheduled task
      const task = cron.schedule(cronExpression, async () => {
        await this.executeScheduledPost(guildId, channelId, tagFilter);
      }, {
        scheduled: false, // Don't start immediately
        timezone: 'UTC'
      });

      // Store the job
      this.scheduledJobs.set(guildId, {
        guildId,
        task,
        intervalMinutes
      });

      // Start the task
      task.start();

      console.log(`[Random Content Scheduler] Scheduled guild ${guildId} for posting every ${intervalMinutes} minutes`);
    } catch (error) {
      console.error(`[Random Content Scheduler] Failed to schedule guild ${guildId}:`, error);
    }
  }

  /**
   * Unschedule random content posting for a guild
   */
  async unscheduleGuild(guildId: string): Promise<void> {
    const existingJob = this.scheduledJobs.get(guildId);
    
    if (existingJob) {
      existingJob.task.stop();
      existingJob.task.destroy();
      this.scheduledJobs.delete(guildId);
      console.log(`[Random Content Scheduler] Unscheduled guild ${guildId}`);
    }
  }

  /**
   * Update schedule for a guild
   */
  async updateGuildSchedule(
    guildId: string, 
    intervalMinutes: number, 
    channelId: string, 
    tagFilter?: string[]
  ): Promise<void> {
    await this.scheduleGuild(guildId, intervalMinutes, channelId, tagFilter);
  }

  /**
   * Execute a scheduled content post
   */
  private async executeScheduledPost(
    guildId: string, 
    channelId: string, 
    tagFilter?: string[]
  ): Promise<void> {
    try {
      console.log(`[Random Content Scheduler] Executing scheduled post for guild ${guildId}`);
      
      const success = await postRandomContent(this.client, guildId, channelId, tagFilter);
      
      if (success) {
        console.log(`[Random Content Scheduler] Successfully posted content for guild ${guildId}`);
      } else {
        console.log(`[Random Content Scheduler] No content available for guild ${guildId}`);
      }
    } catch (error) {
      console.error(`[Random Content Scheduler] Failed to post content for guild ${guildId}:`, error);
      
      // If there are repeated failures, consider temporarily disabling the schedule
      await this.handleScheduleError(guildId, error);
    }
  }

  /**
   * Handle scheduling errors
   */
  private async handleScheduleError(guildId: string, error: any): Promise<void> {
    try {
      // For now, just log the error. In the future, we could implement:
      // - Error counting and temporary disabling
      // - Notification to guild admins
      // - Automatic retry with backoff
      
      console.error(`[Random Content Scheduler] Handling error for guild ${guildId}:`, error);
      
      // If it's a permission error or channel not found, disable the schedule
      if (error.message?.includes('Missing Permissions') || 
          error.message?.includes('Unknown Channel') ||
          error.message?.includes('Invalid or inaccessible channel')) {
        
        console.log(`[Random Content Scheduler] Disabling schedule for guild ${guildId} due to channel/permission issues`);
        
        // Disable in database
        await RandomContentConfig.findOneAndUpdate(
          { guildId },
          { enabled: false },
          { new: true }
        );
        
        // Remove from scheduler
        await this.unscheduleGuild(guildId);
      }
    } catch (handlerError) {
      console.error(`[Random Content Scheduler] Error in error handler for guild ${guildId}:`, handlerError);
    }
  }

  /**
   * Get status of all scheduled jobs
   */
  getScheduleStatus(): Array<{ guildId: string; intervalMinutes: number; isRunning: boolean }> {
    return Array.from(this.scheduledJobs.values()).map(job => ({
      guildId: job.guildId,
      intervalMinutes: job.intervalMinutes,
      isRunning: job.task.running
    }));
  }

  /**
   * Stop all scheduled jobs
   */
  async stopAll(): Promise<void> {
    console.log('[Random Content Scheduler] Stopping all scheduled jobs...');
    
    for (const [guildId, job] of this.scheduledJobs) {
      job.task.stop();
      job.task.destroy();
    }
    
    this.scheduledJobs.clear();
    this.isInitialized = false;
    
    console.log('[Random Content Scheduler] All jobs stopped');
  }

  /**
   * Reload schedules from database
   */
  async reloadSchedules(): Promise<void> {
    console.log('[Random Content Scheduler] Reloading schedules from database...');
    
    // Stop all current jobs
    await this.stopAll();
    
    // Reinitialize
    await this.initialize();
  }
}

export default RandomContentScheduler;
