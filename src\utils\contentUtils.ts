import { Message, Attachment, Client } from 'discord.js';
import { ValidationError } from './errorHandler';

/**
 * Supported image file extensions
 */
export const SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

/**
 * Maximum file size for attachments (8MB in bytes)
 */
export const MAX_ATTACHMENT_SIZE = 8 * 1024 * 1024;

/**
 * Maximum number of attachments per submission
 */
export const MAX_ATTACHMENTS_PER_SUBMISSION = 10;

/**
 * Maximum number of URLs per submission
 */
export const MAX_URLS_PER_SUBMISSION = 10;

/**
 * Maximum number of tags per submission
 */
export const MAX_TAGS_PER_SUBMISSION = 20;

/**
 * Maximum tag length
 */
export const MAX_TAG_LENGTH = 50;

/**
 * Check if a URL points to an image
 */
export function isImageUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname.toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.some(ext => pathname.endsWith(ext));
    } catch {
        return false;
    }
}

/**
 * Check if an attachment is an image
 */
export function isImageAttachment(attachment: Attachment): boolean {
    if (!attachment.contentType) {
        return isImageUrl(attachment.url);
    }
    return attachment.contentType.startsWith('image/');
}

/**
 * Validate URL format and accessibility
 */
export function validateUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        // Only allow http and https protocols
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}

/**
 * Validate Discord CDN URL
 */
export function validateDiscordCdnUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname === 'cdn.discordapp.com' || 
               urlObj.hostname === 'media.discordapp.net';
    } catch {
        return false;
    }
}

/**
 * Extract and validate hashtags from text
 */
export function extractHashtags(text: string): string[] {
    if (!text) return [];
    
    const hashtagRegex = /#([a-zA-Z0-9_-]+)/g;
    const matches = text.match(hashtagRegex);
    
    if (!matches) return [];
    
    return matches
        .map(tag => tag.substring(1).toLowerCase()) // Remove # and convert to lowercase
        .filter(tag => tag.length > 0 && tag.length <= MAX_TAG_LENGTH)
        .filter((tag, index, array) => array.indexOf(tag) === index) // Remove duplicates
        .slice(0, MAX_TAGS_PER_SUBMISSION); // Limit number of tags
}

/**
 * Remove hashtags from text
 */
export function removeHashtags(text: string): string {
    if (!text) return '';
    return text.replace(/#[a-zA-Z0-9_-]+/g, '').trim();
}

/**
 * Extract URLs from text
 */
export function extractUrls(text: string): string[] {
    if (!text) return [];
    
    const urlRegex = /https?:\/\/[^\s]+/g;
    const matches = text.match(urlRegex);
    
    if (!matches) return [];
    
    return matches
        .filter(url => validateUrl(url))
        .filter((url, index, array) => array.indexOf(url) === index) // Remove duplicates
        .slice(0, MAX_URLS_PER_SUBMISSION); // Limit number of URLs
}

/**
 * Clean text content by removing bot mentions and extra whitespace
 */
export function cleanTextContent(text: string, botId?: string): string {
    if (!text) return '';
    
    let cleaned = text;
    
    // Remove bot mentions
    if (botId) {
        const botMentionRegex = new RegExp(`<@!?${botId}>`, 'g');
        cleaned = cleaned.replace(botMentionRegex, '');
    } else {
        // Remove all user mentions as fallback
        cleaned = cleaned.replace(/<@!?\d+>/g, '');
    }
    
    // Remove extra whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    
    return cleaned;
}

/**
 * Validate attachment size and type
 */
export function validateAttachment(attachment: Attachment): { valid: boolean; reason?: string } {
    // Check file size
    if (attachment.size > MAX_ATTACHMENT_SIZE) {
        return {
            valid: false,
            reason: `File size (${(attachment.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (8MB)`
        };
    }
    
    // Check if it's a supported type (images or common file types)
    const supportedTypes = [
        'image/', 'text/', 'application/pdf', 'application/json',
        'video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav'
    ];
    
    if (attachment.contentType && !supportedTypes.some(type => attachment.contentType!.startsWith(type))) {
        return {
            valid: false,
            reason: `File type (${attachment.contentType}) is not supported`
        };
    }
    
    return { valid: true };
}

/**
 * Check if message contains valid content for submission
 */
export function hasValidContent(message: Message, botId: string): boolean {
    const cleanText = cleanTextContent(message.content, botId);
    const hasText = cleanText.length > 0;
    const hasAttachments = message.attachments.size > 0;
    const hasUrls = extractUrls(message.content).length > 0;
    
    return hasText || hasAttachments || hasUrls;
}

/**
 * Get content type description for display
 */
export function getContentTypeDescription(contentType: string): string {
    switch (contentType) {
        case 'text':
            return 'Text Content';
        case 'image':
            return 'Image Content';
        case 'link':
            return 'Link Content';
        case 'mixed':
            return 'Mixed Content';
        default:
            return 'Unknown Content';
    }
}

/**
 * Format content for display in embeds
 */
export function formatContentForDisplay(content: {
    contentText?: string;
    contentUrls?: string[];
    contentAttachments?: string[];
    tags?: string[];
}): {
    text?: string;
    urls?: string;
    attachments?: string;
    tags?: string;
} {
    const result: any = {};
    
    if (content.contentText) {
        result.text = content.contentText.length > 100 
            ? content.contentText.substring(0, 100) + '...'
            : content.contentText;
    }
    
    if (content.contentUrls && content.contentUrls.length > 0) {
        result.urls = content.contentUrls.slice(0, 3).join('\n');
        if (content.contentUrls.length > 3) {
            result.urls += `\n... and ${content.contentUrls.length - 3} more`;
        }
    }
    
    if (content.contentAttachments && content.contentAttachments.length > 0) {
        result.attachments = content.contentAttachments.slice(0, 3).join('\n');
        if (content.contentAttachments.length > 3) {
            result.attachments += `\n... and ${content.contentAttachments.length - 3} more`;
        }
    }
    
    if (content.tags && content.tags.length > 0) {
        result.tags = content.tags.map(tag => `#${tag}`).join(' ');
    }
    
    return result;
}

/**
 * Check if user has permission to manage content (admin or content owner)
 */
export function canManageContent(userId: string, contentUserId: string, isAdmin: boolean): boolean {
    return isAdmin || userId === contentUserId;
}

/**
 * Sanitize tag for storage
 */
export function sanitizeTag(tag: string): string {
    return tag
        .toLowerCase()
        .replace(/[^a-z0-9_-]/g, '')
        .substring(0, MAX_TAG_LENGTH);
}

/**
 * Validate tag format
 */
export function validateTag(tag: string): boolean {
    return /^[a-zA-Z0-9_-]+$/.test(tag) && tag.length > 0 && tag.length <= MAX_TAG_LENGTH;
}
