import User from '../models/User';
import Transaction from '../models/Transaction';
import { DatabaseError } from '../utils/errorHandler';
import mongoose from 'mongoose';
import { checkAndAssignRoles, sendRoleAchievementNotifications } from './roleAssignmentService';
import { Client } from 'discord.js';

function logDatabaseOperation(operation: string, details: any) {
    console.log(`[Database Operation] ${operation}:`, JSON.stringify(details, null, 2));
}

export async function adjustBalance(
    discordId: string,
    amount: number,
    type: 'give' | 'fine' | 'pay' | 'role_achievement' | 'reaction' | 'tax' | 'starter_balance',
    details?: string,
    client?: Client,
    guildId?: string
) {
    // Input validation
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new DatabaseError('Invalid Discord ID provided');
    }

    if (typeof amount !== 'number' || isNaN(amount)) {
        throw new DatabaseError('Invalid amount provided');
    }

    const session = await mongoose.startSession();
    logDatabaseOperation('Starting Transaction', { discordId, amount, type, details });

    try {
        await session.withTransaction(async () => {
            logDatabaseOperation('Finding/Updating User', { discordId, amount });

            // Validate discordId before database operation
            const trimmedDiscordId = discordId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty after trimming');
            }

            // Atomic update or insert
            const user = await User.findOneAndUpdate(
                { discordId: trimmedDiscordId },
                {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: trimmedDiscordId }
                },
                {
                    new: true,
                    upsert: true,
                    runValidators: true,
                    session
                }
            );

            logDatabaseOperation('Creating Transaction Record', {
                discordId: trimmedDiscordId,
                type,
                amount,
                details
            });

            // Create transaction record in same transaction
            await Transaction.create([{
                discordId: trimmedDiscordId,
                type,
                amount,
                details,
                timestamp: new Date()
            }], { session });

            logDatabaseOperation('Transaction Complete', { userId: user?._id, newBalance: user?.balance });

            // Check for role achievements if balance increased and we have client/guild info
            if (amount > 0 && client && guildId && user) {
                // Schedule role checking after transaction completes
                setImmediate(async () => {
                    try {
                        const roleResult = await checkAndAssignRoles(client, trimmedDiscordId, guildId, user.balance);
                        if (roleResult) {
                            await sendRoleAchievementNotifications(roleResult, client);
                        }
                    } catch (error) {
                        console.error('Error checking roles after balance adjustment:', error);
                    }
                });
            }

            return user;
        });
    } catch (error) {
        console.error('Error in adjustBalance:', error);
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new DatabaseError('Transaction validation failed', error);
        } else if (error instanceof Error && error.name === 'MongoServerError') {
            const err = error as any;
            if (err.code === 11000) {
                throw new DatabaseError('Transaction conflict detected', error);
            }
            throw new DatabaseError('Database operation failed', error);
        } else if (error instanceof Error) {
            throw new DatabaseError('Transaction processing failed', error);
        }
        throw new DatabaseError('Unexpected error during transaction');
    } finally {
        await session.endSession();
    }
}

export async function getLeaderboard(limit = 10) {
    try {
        logDatabaseOperation('Fetching Leaderboard', { limit });
        const users = await User.find().sort({ balance: -1 }).limit(limit);
        logDatabaseOperation('Leaderboard Fetched', { count: users.length });
        return users;
    } catch (error) {
        if (error instanceof Error) {
            throw new DatabaseError('Failed to fetch leaderboard', error);
        }
        throw new DatabaseError('Failed to fetch leaderboard');
    }
}

export async function getTransactionHistory(discordId: string, limit = 10) {
    try {
        logDatabaseOperation('Fetching Transaction History', { discordId, limit });
        const transactions = await Transaction.find({ discordId }).sort({ timestamp: -1 }).limit(limit);
        logDatabaseOperation('Transaction History Fetched', { count: transactions.length });
        return transactions;
    } catch (error) {
        if (error instanceof Error) {
            throw new DatabaseError('Failed to fetch transaction history', error);
        }
        throw new DatabaseError('Failed to fetch transaction history');
    }
}

// Helper function to ensure user exists
export async function ensureUser(discordId: string) {
    // Input validation
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new DatabaseError('Invalid Discord ID provided to ensureUser');
    }

    try {
        const trimmedDiscordId = discordId.trim();
        logDatabaseOperation('Ensuring User Exists', { discordId: trimmedDiscordId });

        const user = await User.findOneAndUpdate(
            { discordId: trimmedDiscordId },
            { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } },
            {
                upsert: true,
                new: true,
                runValidators: true
            }
        );
        logDatabaseOperation('User Ensured', { userId: user._id, isNew: user.isNew });
        return user;
    } catch (error) {
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new DatabaseError('Invalid user data format', error);
        } else if (error instanceof Error && error.name === 'MongoServerError' && (error as any).code === 11000) {
            throw new DatabaseError('User already exists', error);
        } else if (error instanceof Error) {
            throw new DatabaseError('Failed to create/fetch user', error);
        }
        throw new DatabaseError('Unexpected error while creating/fetching user');
    }
}
