import { Client, TextChannel, EmbedBuilder } from 'discord.js';
import ContentSubmission, { IContentSubmission } from '../models/ContentSubmission';
import RandomContentConfig, { IRandomContentConfig } from '../models/RandomContentConfig';
import { DatabaseError } from '../utils/errorHandler';
import { createBaseEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';
import mongoose from 'mongoose';

// Global reference to scheduler (will be set from index.ts)
let globalScheduler: any = null;

export function setGlobalScheduler(scheduler: any) {
  globalScheduler = scheduler;
}

function logDatabaseOperation(operation: string, details: any) {
    console.log(`[Random Content] ${operation}:`, JSON.stringify(details, null, 2));
}

/**
 * Get or create random content configuration for a guild
 */
export async function getOrCreateConfig(guildId: string): Promise<IRandomContentConfig> {
    try {
        let config = await RandomContentConfig.findOne({ guildId });
        
        if (!config) {
            config = await RandomContentConfig.create({
                guildId,
                postingIntervalMinutes: 60,
                enabled: false
            });
            
            logDatabaseOperation('Config Created', { guildId });
        }
        
        return config;
    } catch (error) {
        throw new DatabaseError('Failed to get or create random content configuration', error.message);
    }
}

/**
 * Update random content configuration
 */
export async function updateConfig(
    guildId: string,
    updates: Partial<IRandomContentConfig>
): Promise<IRandomContentConfig> {
    try {
        const config = await RandomContentConfig.findOneAndUpdate(
            { guildId },
            { ...updates, updatedAt: new Date() },
            { new: true, upsert: true }
        );

        logDatabaseOperation('Config Updated', { guildId, updates });

        // Update scheduler if available
        if (globalScheduler && config) {
            if (config.enabled && config.targetChannelId) {
                await globalScheduler.scheduleGuild(
                    guildId,
                    config.postingIntervalMinutes,
                    config.targetChannelId,
                    config.tagFilter
                );
            } else {
                await globalScheduler.unscheduleGuild(guildId);
            }
        }

        return config!;
    } catch (error) {
        throw new DatabaseError('Failed to update random content configuration', error.message);
    }
}

/**
 * Select random content using weighted algorithm
 */
export async function selectRandomContent(
    guildId: string,
    tagFilter?: string[]
): Promise<IContentSubmission | null> {
    try {
        const query: any = { guildId, isActive: true };
        
        // Apply tag filter if provided
        if (tagFilter && tagFilter.length > 0) {
            query.tags = { $in: tagFilter };
        }

        // Get all eligible content
        const allContent = await ContentSubmission.find(query).lean();
        
        if (allContent.length === 0) {
            return null;
        }

        // Calculate weights (favor less-used content)
        const weightedContent = allContent.map(content => ({
            content,
            weight: 1 / (content.usageCount + 1)
        }));

        // Calculate total weight
        const totalWeight = weightedContent.reduce((sum, item) => sum + item.weight, 0);

        // Select random content based on weights
        let random = Math.random() * totalWeight;
        let selectedContent: IContentSubmission | null = null;

        for (const item of weightedContent) {
            random -= item.weight;
            if (random <= 0) {
                selectedContent = item.content as IContentSubmission;
                break;
            }
        }

        // Fallback to first item if something goes wrong
        if (!selectedContent) {
            selectedContent = allContent[0] as IContentSubmission;
        }

        // Update usage statistics
        await ContentSubmission.findByIdAndUpdate(selectedContent._id, {
            $inc: { usageCount: 1 },
            lastUsedAt: new Date()
        });

        logDatabaseOperation('Content Selected', {
            contentId: selectedContent._id,
            contentType: selectedContent.contentType,
            usageCount: selectedContent.usageCount + 1
        });

        return selectedContent;
    } catch (error) {
        throw new DatabaseError('Failed to select random content', error.message);
    }
}

/**
 * Create embed for content posting
 */
export function createContentEmbed(content: IContentSubmission, client: Client): EmbedBuilder {
    const embed = createBaseEmbed(
        `${EMOJIS.MISC.SPARKLES} Random Content`,
        undefined,
        COLORS.PRIMARY
    );

    // Add text content if available
    if (content.contentText) {
        embed.setDescription(content.contentText);
    }

    // Add image if available
    if (content.contentAttachments.length > 0) {
        const imageUrl = content.contentAttachments.find(url => 
            /\.(jpg|jpeg|png|gif|webp)(\?|$)/i.test(url)
        );
        if (imageUrl) {
            embed.setImage(imageUrl);
        }
    }

    // Add URLs as fields
    if (content.contentUrls.length > 0) {
        embed.addFields({
            name: `${EMOJIS.MISC.LINK} Links`,
            value: content.contentUrls.slice(0, 5).join('\n'),
            inline: false
        });
    }

    // Add tags if available
    if (content.tags && content.tags.length > 0) {
        embed.addFields({
            name: `${EMOJIS.MISC.TAG} Tags`,
            value: content.tags.map(tag => `#${tag}`).join(' '),
            inline: false
        });
    }

    // Add footer with usage stats
    embed.setFooter({
        text: `Used ${content.usageCount} time${content.usageCount !== 1 ? 's' : ''} • Content ID: ${content._id}`
    });

    return embed;
}

/**
 * Post random content to a channel
 */
export async function postRandomContent(
    client: Client,
    guildId: string,
    channelId: string,
    tagFilter?: string[]
): Promise<boolean> {
    try {
        const channel = await client.channels.fetch(channelId) as TextChannel;
        
        if (!channel || !channel.isTextBased()) {
            throw new Error('Invalid or inaccessible channel');
        }

        // Check bot permissions
        const permissions = channel.permissionsFor(client.user!);
        if (!permissions?.has(['SendMessages', 'EmbedLinks'])) {
            throw new Error('Bot lacks necessary permissions in target channel');
        }

        // Select random content
        const content = await selectRandomContent(guildId, tagFilter);
        
        if (!content) {
            logDatabaseOperation('No Content Available', { guildId, tagFilter });
            return false;
        }

        // Create and send embed
        const embed = createContentEmbed(content, client);
        
        // Send content
        const messageOptions: any = { embeds: [embed] };
        
        // Add additional attachments if any
        if (content.contentAttachments.length > 1) {
            const additionalAttachments = content.contentAttachments.slice(1, 5); // Max 4 additional
            if (additionalAttachments.length > 0) {
                embed.addFields({
                    name: `${EMOJIS.MISC.ATTACHMENT} Additional Attachments`,
                    value: additionalAttachments.join('\n'),
                    inline: false
                });
            }
        }

        await channel.send(messageOptions);

        logDatabaseOperation('Content Posted', {
            guildId,
            channelId,
            contentId: content._id,
            contentType: content.contentType
        });

        return true;
    } catch (error) {
        logDatabaseOperation('Content Posting Error', {
            guildId,
            channelId,
            error: error.message
        });
        
        throw new DatabaseError('Failed to post random content', error.message);
    }
}

/**
 * Get content statistics for a guild
 */
export async function getContentStats(guildId: string, userId?: string): Promise<{
    totalSubmissions: number;
    activeSubmissions: number;
    totalUsage: number;
    userSubmissions?: number;
    contentByType: Record<string, number>;
    topTags: Array<{ tag: string; count: number }>;
}> {
    try {
        const baseQuery = { guildId, isActive: true };
        const userQuery = userId ? { ...baseQuery, userId } : baseQuery;

        const [
            totalSubmissions,
            activeSubmissions,
            userSubmissions,
            contentByType,
            allContent
        ] = await Promise.all([
            ContentSubmission.countDocuments({ guildId }),
            ContentSubmission.countDocuments(baseQuery),
            userId ? ContentSubmission.countDocuments(userQuery) : Promise.resolve(undefined),
            ContentSubmission.aggregate([
                { $match: baseQuery },
                { $group: { _id: '$contentType', count: { $sum: 1 } } }
            ]),
            ContentSubmission.find(baseQuery, 'usageCount tags').lean()
        ]);

        // Calculate total usage
        const totalUsage = allContent.reduce((sum, content) => sum + content.usageCount, 0);

        // Calculate content by type
        const contentTypeStats: Record<string, number> = {};
        contentByType.forEach((item: any) => {
            contentTypeStats[item._id] = item.count;
        });

        // Calculate top tags
        const tagCounts: Record<string, number> = {};
        allContent.forEach(content => {
            if (content.tags) {
                content.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });

        const topTags = Object.entries(tagCounts)
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        return {
            totalSubmissions,
            activeSubmissions,
            totalUsage,
            userSubmissions,
            contentByType: contentTypeStats,
            topTags
        };
    } catch (error) {
        throw new DatabaseError('Failed to get content statistics', error.message);
    }
}
