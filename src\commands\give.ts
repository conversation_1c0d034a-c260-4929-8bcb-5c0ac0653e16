import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { adjustBalance } from '../services/economyService';
import { withErrorHandler, ValidationError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, createSuccessEmbed, addUserInfo, formatCoins, EMOJIS } from '../utils/embedBuilder';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('give')
        .setDescription('Give coins to a user (admin only)')
        .addUserOption(option => option.setName('user').setDescription('User to give coins to').setRequired(true))
        .addIntegerOption(option => option.setName('amount').setDescription('Amount to give').setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);

        // Input validation
        if (amount <= 0) {
            throw new ValidationError('Amount must be greater than zero.');
        }
        if (targetUser.bot) {
            throw new ValidationError('You cannot give coins to a bot.');
        }

        // adjustBalance already handles user creation with upsert, no need for ensureUser
        await adjustBalance(
            targetUser.id,
            amount,
            'give',
            `Given by admin ${interaction.user.tag}`,
            interaction.client,
            interaction.guild?.id
        );

        // Create rich admin success embed
        const embed = createSuccessEmbed('Coins Awarded Successfully!')
            .setDescription(
                `${EMOJIS.ADMIN.HAMMER} **Administrative Action Completed**\n\n` +
                `${formatCoins(amount)} has been awarded to **${targetUser.displayName}**!`
            )
            .addFields(
                {
                    name: `${EMOJIS.ADMIN.KEY} Administrator`,
                    value: `**${interaction.user.displayName}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.ACTIONS.TARGET} Recipient`,
                    value: `**${targetUser.displayName}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.ECONOMY.COINS} Amount Awarded`,
                    value: formatCoins(amount),
                    inline: true
                },
                {
                    name: `${EMOJIS.MISC.SCROLL} Transaction Details`,
                    value: `Admin reward issued by ${interaction.user.displayName}`,
                    inline: false
                },
                {
                    name: `${EMOJIS.MISC.CLOCK} Transaction Time`,
                    value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: false
                }
            )
            .setFooter({
                text: 'Administrative action logged in transaction history'
            });

        // Add admin's avatar to embed
        addUserInfo(embed, interaction.user);

        await interaction.reply({
            embeds: [embed],
            ephemeral: false
        });
    })
};
