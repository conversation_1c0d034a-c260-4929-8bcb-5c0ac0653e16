import { Schema, model, Document } from 'mongoose';

export interface IContentSubmission extends Document {
  guildId: string;
  userId: string;
  contentText?: string;
  contentUrls?: string[];
  contentAttachments?: string[];
  contentType: 'text' | 'image' | 'link' | 'mixed';
  tags?: string[];
  createdAt: Date;
  usageCount: number;
  lastUsedAt?: Date;
  isActive: boolean;
}

const contentSubmissionSchema = new Schema<IContentSubmission>({
  guildId: {
    type: String,
    required: [true, 'Guild ID is required'],
    index: true,
    validate: {
      validator: function(v: string): boolean {
        return !!(v && v.trim().length > 0);
      },
      message: 'Guild ID cannot be empty'
    }
  },
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    validate: {
      validator: function(v: string): boolean {
        return !!(v && v.trim().length > 0);
      },
      message: 'User ID cannot be empty'
    }
  },
  contentText: {
    type: String,
    maxlength: [2000, 'Content text cannot exceed 2000 characters'],
    default: null
  },
  contentUrls: {
    type: [String],
    default: [],
    validate: {
      validator: function(urls: string[]): boolean {
        if (!urls || urls.length === 0) return true;
        return urls.every(url => {
          try {
            new URL(url);
            return true;
          } catch {
            return false;
          }
        });
      },
      message: 'All URLs must be valid'
    }
  },
  contentAttachments: {
    type: [String],
    default: [],
    validate: {
      validator: function(attachments: string[]): boolean {
        if (!attachments || attachments.length === 0) return true;
        return attachments.every(url => {
          try {
            new URL(url);
            return url.includes('cdn.discordapp.com') || url.includes('media.discordapp.net');
          } catch {
            return false;
          }
        });
      },
      message: 'All attachment URLs must be valid Discord CDN URLs'
    }
  },
  contentType: {
    type: String,
    enum: ['text', 'image', 'link', 'mixed'],
    required: [true, 'Content type is required'],
    index: true
  },
  tags: {
    type: [String],
    default: [],
    validate: {
      validator: function(tags: string[]): boolean {
        if (!tags || tags.length === 0) return true;
        return tags.every(tag => 
          typeof tag === 'string' && 
          tag.length > 0 && 
          tag.length <= 50 &&
          /^[a-zA-Z0-9_-]+$/.test(tag)
        );
      },
      message: 'Tags must be alphanumeric with underscores/hyphens and max 50 characters'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  usageCount: {
    type: Number,
    default: 0,
    min: [0, 'Usage count cannot be negative']
  },
  lastUsedAt: {
    type: Date,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  }
});

// Compound indexes for efficient queries
contentSubmissionSchema.index({ guildId: 1, isActive: 1 });
contentSubmissionSchema.index({ guildId: 1, contentType: 1, isActive: 1 });
contentSubmissionSchema.index({ guildId: 1, tags: 1, isActive: 1 });
contentSubmissionSchema.index({ userId: 1, guildId: 1 });

export default model<IContentSubmission>('ContentSubmission', contentSubmissionSchema);
