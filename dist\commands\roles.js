"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const User_2 = __importDefault(require("../models/User"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleAssignmentService_1 = require("../services/roleAssignmentService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('roles')
        .setDescription('Display all role achievements available to unlock'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const roles = await User_1.RoleForSale.find().sort({ price: 1 }); // Sort by price ascending
            const discordId = interaction.user.id;
            if (!roles.length) {
                const embed = (0, embedBuilder_1.createEconomyEmbed)('Role Achievements')
                    .setDescription(`${embedBuilder_1.EMOJIS.ROLES.MASK} No role achievements are currently available to unlock!\n\nCheck back later or ask an administrator to add some role achievements.`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            // Get user's current balance
            const user = await User_2.default.findOne({ discordId });
            const currentBalance = user?.balance || 0;
            // Get user's current achievement roles
            let userAchievements = [];
            if (interaction.guild) {
                const member = await interaction.guild.members.fetch(discordId);
                userAchievements = await (0, roleAssignmentService_1.getUserAchievementRoles)(member);
            }
            // Create rich embed with role information
            const embed = (0, embedBuilder_1.createEconomyEmbed)('Phalanx Role Achievements')
                .setDescription(`${embedBuilder_1.EMOJIS.ROLES.SHIELD} **Role Achievement Progress**\n\nRole achievements are automatically unlocked when you reach the required PLC balance! Your PLC balance represents your lifetime achievements and is never spent.`)
                .setThumbnail('https://cdn.discordapp.com/emojis/1234567890123456789.png'); // You can add a custom shop icon
            // Add user's current balance
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Your Current Balance`,
                value: (0, embedBuilder_1.formatCoins)(currentBalance),
                inline: true
            });
            // Add achieved roles section
            if (userAchievements.length > 0) {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.SUCCESS.CROWN} Unlocked Achievements (${userAchievements.length})`,
                    value: userAchievements.map(achievement => `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${achievement.roleName}** - ${(0, embedBuilder_1.formatCoins)(achievement.price)}`).join('\n'),
                    inline: false
                });
            }
            // Add available roles section
            const availableRoles = roles.filter(role => !userAchievements.some(achievement => achievement.roleId === role.roleId));
            if (availableRoles.length > 0) {
                const roleFields = availableRoles.map(role => {
                    const canUnlock = currentBalance >= role.price;
                    const statusEmoji = canUnlock ? embedBuilder_1.EMOJIS.SUCCESS.STAR : embedBuilder_1.EMOJIS.MISC.CLOCK;
                    const statusText = canUnlock ? '**Ready to unlock!**' : `Need ${(0, embedBuilder_1.formatCoins)(role.price - currentBalance)} more`;
                    return `${statusEmoji} **${role.name}** - ${(0, embedBuilder_1.formatCoins)(role.price)}\n${statusText}`;
                });
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Available Achievements (${availableRoles.length})`,
                    value: roleFields.join('\n\n'),
                    inline: false
                });
            }
            // Add summary information
            const totalRoles = roles.length;
            const unlockedCount = userAchievements.length;
            const progressPercentage = Math.round((unlockedCount / totalRoles) * 100);
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Achievement Progress`,
                value: `**${unlockedCount}/${totalRoles}** achievements unlocked (${progressPercentage}%)\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Your Balance: **${(0, embedBuilder_1.formatCoins)(currentBalance)}** coins`,
                inline: false
            });
            embed.setFooter({
                text: 'Role achievements are automatically unlocked when you reach the required PLC balance!'
            });
            // Add user info to embed
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            // Create quick action buttons
            const actionButtons = (0, embedBuilder_1.createQuickActionButtons)();
            const components = [actionButtons];
            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to fetch roles.');
            }
        }
    })
};
