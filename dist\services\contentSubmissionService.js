"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractContentFromMessage = extractContentFromMessage;
exports.validateContent = validateContent;
exports.storeContentSubmission = storeContentSubmission;
exports.getUserContentSubmissions = getUserContentSubmissions;
exports.deleteContentSubmission = deleteContentSubmission;
const ContentSubmission_1 = __importDefault(require("../models/ContentSubmission"));
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
function logDatabaseOperation(operation, details) {
    console.log(`[Content Submission] ${operation}:`, JSON.stringify(details, null, 2));
}
/**
 * Extract and validate content from a Discord message
 */
function extractContentFromMessage(message) {
    const result = {
        contentUrls: [],
        contentAttachments: [],
        contentType: 'text',
        tags: []
    };
    // Extract text content (remove bot mentions)
    let cleanText = message.content;
    if (cleanText) {
        // Remove bot mentions
        cleanText = cleanText.replace(/<@!?\d+>/g, '').trim();
        // Extract hashtags
        const hashtagMatches = cleanText.match(/#[a-zA-Z0-9_-]+/g);
        if (hashtagMatches) {
            result.tags = hashtagMatches.map(tag => tag.substring(1).toLowerCase());
            // Remove hashtags from content text
            cleanText = cleanText.replace(/#[a-zA-Z0-9_-]+/g, '').trim();
        }
        if (cleanText.length > 0) {
            result.contentText = cleanText.substring(0, 2000); // Enforce character limit
        }
    }
    // Extract URLs from text
    const urlRegex = /https?:\/\/[^\s]+/g;
    const urlMatches = result.contentText?.match(urlRegex) || [];
    result.contentUrls = urlMatches.filter(url => {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    });
    // Extract attachments
    if (message.attachments.size > 0) {
        result.contentAttachments = message.attachments.map((attachment) => attachment.url);
    }
    // Determine content type
    const hasText = !!(result.contentText && result.contentText.length > 0);
    const hasUrls = result.contentUrls.length > 0;
    const hasAttachments = result.contentAttachments.length > 0;
    const hasImages = result.contentAttachments.some(url => /\.(jpg|jpeg|png|gif|webp)(\?|$)/i.test(url)) || result.contentUrls.some(url => /\.(jpg|jpeg|png|gif|webp)(\?|$)/i.test(url));
    if (hasImages && !hasText && !hasUrls) {
        result.contentType = 'image';
    }
    else if (hasUrls && !hasText && !hasAttachments) {
        result.contentType = 'link';
    }
    else if ((hasText || hasUrls || hasAttachments) && (hasImages || hasUrls || hasAttachments)) {
        result.contentType = 'mixed';
    }
    else {
        result.contentType = 'text';
    }
    return result;
}
/**
 * Validate processed content before storage
 */
function validateContent(content) {
    const hasText = !!(content.contentText && content.contentText.length > 0);
    const hasUrls = content.contentUrls.length > 0;
    const hasAttachments = content.contentAttachments.length > 0;
    if (!hasText && !hasUrls && !hasAttachments) {
        throw new errorHandler_1.ValidationError('Message must contain text, URLs, or attachments');
    }
    if (content.contentText && content.contentText.length > 2000) {
        throw new errorHandler_1.ValidationError('Text content cannot exceed 2000 characters');
    }
    if (content.contentUrls.length > 10) {
        throw new errorHandler_1.ValidationError('Cannot have more than 10 URLs per submission');
    }
    if (content.contentAttachments.length > 10) {
        throw new errorHandler_1.ValidationError('Cannot have more than 10 attachments per submission');
    }
    if (content.tags.length > 20) {
        throw new errorHandler_1.ValidationError('Cannot have more than 20 tags per submission');
    }
}
/**
 * Store content submission in database
 */
async function storeContentSubmission(guildId, userId, content) {
    const session = await mongoose_1.default.startSession();
    try {
        logDatabaseOperation('Starting Content Submission', { guildId, userId, contentType: content.contentType });
        // Validate content
        validateContent(content);
        // Check if user has too many submissions (rate limiting)
        const userSubmissionCount = await ContentSubmission_1.default.countDocuments({
            guildId,
            userId,
            isActive: true,
            createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        });
        if (userSubmissionCount >= 50) {
            throw new errorHandler_1.ValidationError('You can only submit 50 pieces of content per day');
        }
        const result = await session.withTransaction(async () => {
            const submission = await ContentSubmission_1.default.create([{
                    guildId,
                    userId,
                    contentText: content.contentText,
                    contentUrls: content.contentUrls,
                    contentAttachments: content.contentAttachments,
                    contentType: content.contentType,
                    tags: content.tags,
                    createdAt: new Date(),
                    usageCount: 0,
                    isActive: true
                }], { session });
            logDatabaseOperation('Content Submission Created', {
                submissionId: submission[0]._id,
                contentType: content.contentType,
                tagsCount: content.tags.length
            });
            return submission[0];
        });
        return result;
    }
    catch (error) {
        logDatabaseOperation('Content Submission Error', { error: error.message });
        if (error instanceof errorHandler_1.ValidationError) {
            throw error;
        }
        throw new errorHandler_1.DatabaseError('Failed to store content submission', error.message);
    }
    finally {
        await session.endSession();
    }
}
/**
 * Get content submissions for a user
 */
async function getUserContentSubmissions(guildId, userId, page = 1, limit = 10, tagFilter) {
    try {
        const skip = (page - 1) * limit;
        const query = { guildId, userId, isActive: true };
        if (tagFilter) {
            query.tags = { $in: [tagFilter.toLowerCase()] };
        }
        const [submissions, total] = await Promise.all([
            ContentSubmission_1.default.find(query)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .lean(),
            ContentSubmission_1.default.countDocuments(query)
        ]);
        return {
            submissions: submissions,
            total,
            hasMore: total > skip + submissions.length
        };
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to retrieve user content submissions', error.message);
    }
}
/**
 * Delete a content submission
 */
async function deleteContentSubmission(submissionId, userId, guildId, isAdmin = false) {
    try {
        const query = { _id: submissionId, guildId };
        // Non-admins can only delete their own content
        if (!isAdmin) {
            query.userId = userId;
        }
        const result = await ContentSubmission_1.default.findOneAndUpdate(query, { isActive: false }, { new: true });
        if (!result) {
            throw new errorHandler_1.ValidationError('Content submission not found or you do not have permission to delete it');
        }
        logDatabaseOperation('Content Submission Deleted', {
            submissionId,
            deletedBy: userId,
            isAdmin
        });
        return true;
    }
    catch (error) {
        if (error instanceof errorHandler_1.ValidationError) {
            throw error;
        }
        throw new errorHandler_1.DatabaseError('Failed to delete content submission', error.message);
    }
}
