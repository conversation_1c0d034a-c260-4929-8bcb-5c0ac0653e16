"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const randomContentConfigSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    targetChannelId: {
        type: String,
        default: null,
        validate: {
            validator: function (v) {
                if (v === null || v === undefined)
                    return true;
                return !!(v && v.trim().length > 0);
            },
            message: 'Target channel ID cannot be empty if provided'
        }
    },
    postingIntervalMinutes: {
        type: Number,
        default: 60,
        min: [5, 'Posting interval must be at least 5 minutes'],
        max: [10080, 'Posting interval cannot exceed 7 days (10080 minutes)']
    },
    enabled: {
        type: Boolean,
        default: false
    },
    tagFilter: {
        type: [String],
        default: [],
        validate: {
            validator: function (tags) {
                if (!tags || tags.length === 0)
                    return true;
                return tags.every(tag => typeof tag === 'string' &&
                    tag.length > 0 &&
                    tag.length <= 50 &&
                    /^[a-zA-Z0-9_-]+$/.test(tag));
            },
            message: 'Tag filters must be alphanumeric with underscores/hyphens and max 50 characters'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
// Update the updatedAt field on save
randomContentConfigSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
exports.default = (0, mongoose_1.model)('RandomContentConfig', randomContentConfigSchema);
