"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const contentSubmissionSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    userId: {
        type: String,
        required: [true, 'User ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'User ID cannot be empty'
        }
    },
    contentText: {
        type: String,
        maxlength: [2000, 'Content text cannot exceed 2000 characters'],
        default: null
    },
    contentUrls: {
        type: [String],
        default: [],
        validate: {
            validator: function (urls) {
                if (!urls || urls.length === 0)
                    return true;
                return urls.every(url => {
                    try {
                        new URL(url);
                        return true;
                    }
                    catch {
                        return false;
                    }
                });
            },
            message: 'All URLs must be valid'
        }
    },
    contentAttachments: {
        type: [String],
        default: [],
        validate: {
            validator: function (attachments) {
                if (!attachments || attachments.length === 0)
                    return true;
                return attachments.every(url => {
                    try {
                        new URL(url);
                        return url.includes('cdn.discordapp.com') || url.includes('media.discordapp.net');
                    }
                    catch {
                        return false;
                    }
                });
            },
            message: 'All attachment URLs must be valid Discord CDN URLs'
        }
    },
    contentType: {
        type: String,
        enum: ['text', 'image', 'link', 'mixed'],
        required: [true, 'Content type is required'],
        index: true
    },
    tags: {
        type: [String],
        default: [],
        validate: {
            validator: function (tags) {
                if (!tags || tags.length === 0)
                    return true;
                return tags.every(tag => typeof tag === 'string' &&
                    tag.length > 0 &&
                    tag.length <= 50 &&
                    /^[a-zA-Z0-9_-]+$/.test(tag));
            },
            message: 'Tags must be alphanumeric with underscores/hyphens and max 50 characters'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    usageCount: {
        type: Number,
        default: 0,
        min: [0, 'Usage count cannot be negative']
    },
    lastUsedAt: {
        type: Date,
        default: null
    },
    isActive: {
        type: Boolean,
        default: true,
        index: true
    }
});
// Compound indexes for efficient queries
contentSubmissionSchema.index({ guildId: 1, isActive: 1 });
contentSubmissionSchema.index({ guildId: 1, contentType: 1, isActive: 1 });
contentSubmissionSchema.index({ guildId: 1, tags: 1, isActive: 1 });
contentSubmissionSchema.index({ userId: 1, guildId: 1 });
exports.default = (0, mongoose_1.model)('ContentSubmission', contentSubmissionSchema);
