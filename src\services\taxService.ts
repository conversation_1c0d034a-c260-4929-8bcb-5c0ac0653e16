import { Client, Guild, GuildMember, Role } from 'discord.js';
import { TaxConfiguration } from '../models/TaxConfiguration';
import { RoleForSale } from '../models/User';
import { adjustBalance } from './economyService';
import User from '../models/User';
import { DatabaseError } from '../utils/errorHandler';
import { createErrorEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';

interface TaxCollectionResult {
    totalProcessed: number;
    totalTaxed: number;
    totalRolesRemoved: number;
    errors: string[];
}

/**
 * Processes tax collection for a specific guild
 */
export async function processTaxCollection(client: Client, guildId: string): Promise<TaxCollectionResult> {
    const result: TaxCollectionResult = {
        totalProcessed: 0,
        totalTaxed: 0,
        totalRolesRemoved: 0,
        errors: []
    };

    try {
        // Get tax configuration for this guild
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        if (!taxConfig) {
            return result; // No tax configuration or disabled
        }

        // Check if it's time to collect taxes
        const now = new Date();
        if (!taxConfig.nextTaxDate || now < taxConfig.nextTaxDate) {
            return result; // Not time yet
        }

        // Get the guild
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            result.errors.push(`Guild ${guildId} not found`);
            return result;
        }

        // Get the taxed role
        const taxedRole = guild.roles.cache.get(taxConfig.roleId);
        if (!taxedRole) {
            result.errors.push(`Taxed role ${taxConfig.roleId} not found in guild ${guild.name}`);
            return result;
        }

        // Get all members with the taxed role
        await guild.members.fetch(); // Ensure all members are cached
        const membersWithRole = taxedRole.members;

        console.log(`[Tax Collection] Processing ${membersWithRole.size} members with role ${taxedRole.name} in guild ${guild.name}`);

        // Process each member
        for (const [memberId, member] of membersWithRole) {
            try {
                result.totalProcessed++;
                
                // Get user's current balance
                const user = await User.findOne({ discordId: memberId });
                const currentBalance = user?.balance || 0;

                if (currentBalance >= taxConfig.amount) {
                    // User can afford the tax - deduct it
                    await adjustBalance(
                        memberId,
                        -taxConfig.amount,
                        'tax',
                        `Tax collection: ${taxConfig.amount} PLC for role ${taxedRole.name}`,
                        client,
                        guild.id
                    );
                    result.totalTaxed++;
                    console.log(`[Tax Collection] Taxed ${taxConfig.amount} PLC from ${member.displayName}`);
                } else {
                    // User cannot afford the tax - remove all purchasable roles and send DM
                    await handleInsufficientFundsForTax(member, taxConfig.amount, currentBalance);
                    result.totalRolesRemoved++;
                    console.log(`[Tax Collection] Removed roles from ${member.displayName} due to insufficient funds`);
                }
            } catch (error) {
                const errorMsg = `Failed to process tax for member ${member.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[Tax Collection] ${errorMsg}`);
            }
        }

        // Update tax configuration with new dates
        taxConfig.lastTaxDate = now;
        taxConfig.nextTaxDate = new Date(now.getTime() + (taxConfig.frequency * 7 * 24 * 60 * 60 * 1000));
        await taxConfig.save();

        console.log(`[Tax Collection] Completed for guild ${guild.name}. Processed: ${result.totalProcessed}, Taxed: ${result.totalTaxed}, Roles Removed: ${result.totalRolesRemoved}`);

    } catch (error) {
        const errorMsg = `Tax collection failed for guild ${guildId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(`[Tax Collection] ${errorMsg}`);
    }

    return result;
}

/**
 * Handles the case where a user cannot afford the tax payment
 */
async function handleInsufficientFundsForTax(member: GuildMember, taxAmount: number, currentBalance: number): Promise<void> {
    try {
        // Get all purchasable roles
        const purchasableRoles = await RoleForSale.find({});
        const purchasableRoleIds = new Set(purchasableRoles.map(role => role.roleId));

        // Find roles to remove (intersection of user's roles and purchasable roles)
        const rolesToRemove = member.roles.cache.filter(role => purchasableRoleIds.has(role.id));

        if (rolesToRemove.size > 0) {
            // Remove all purchasable roles
            await member.roles.remove(rolesToRemove);
            
            // Send DM to user explaining what happened
            try {
                const embed = createErrorEmbed('Tax Payment Failed - Roles Removed')
                    .setDescription(
                        `${EMOJIS.ADMIN.WARNING} **Insufficient Funds for Tax Payment**\n\n` +
                        `You were unable to pay the required tax of **${taxAmount} PLC** ` +
                        `(you only had **${currentBalance} PLC**).\n\n` +
                        `As a result, all your purchasable roles have been removed:\n` +
                        `${rolesToRemove.map(role => `• ${role.name}`).join('\n')}\n\n` +
                        `${EMOJIS.ECONOMY.COINS} You can earn more PLC and repurchase these roles when you have sufficient funds.`
                    )
                    .setColor(COLORS.ERROR)
                    .setFooter({ text: 'Phalanx Order Economy System' });

                await member.send({ embeds: [embed] });
            } catch (dmError) {
                console.error(`[Tax Collection] Failed to send DM to ${member.displayName}:`, dmError);
            }
        }

        // Set balance to 0 if it was negative due to other transactions
        if (currentBalance < 0) {
            await adjustBalance(
                member.id,
                -currentBalance,
                'tax',
                'Balance reset to 0 after tax enforcement',
                member.client,
                member.guild.id
            );
        }

    } catch (error) {
        throw new DatabaseError(`Failed to handle insufficient funds for tax: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Gets the next scheduled tax collection date for a guild
 */
export async function getNextTaxDate(guildId: string): Promise<Date | null> {
    try {
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        return taxConfig?.nextTaxDate || null;
    } catch (error) {
        console.error(`[Tax Service] Failed to get next tax date for guild ${guildId}:`, error);
        return null;
    }
}

/**
 * Checks if tax collection is enabled for a guild
 */
export async function isTaxEnabled(guildId: string): Promise<boolean> {
    try {
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        return !!taxConfig;
    } catch (error) {
        console.error(`[Tax Service] Failed to check tax status for guild ${guildId}:`, error);
        return false;
    }
}
