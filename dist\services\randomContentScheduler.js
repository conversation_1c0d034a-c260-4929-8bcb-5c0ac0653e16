"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_cron_1 = __importDefault(require("node-cron"));
const RandomContentConfig_1 = __importDefault(require("../models/RandomContentConfig"));
const randomContentService_1 = require("./randomContentService");
class RandomContentScheduler {
    constructor(client) {
        this.scheduledJobs = new Map();
        this.isInitialized = false;
        this.client = client;
    }
    /**
     * Initialize the scheduler and load all active configurations
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('[Random Content Scheduler] Already initialized');
            return;
        }
        try {
            console.log('[Random Content Scheduler] Initializing...');
            // Load all active configurations
            const activeConfigs = await RandomContentConfig_1.default.find({ enabled: true });
            for (const config of activeConfigs) {
                if (config.targetChannelId) {
                    await this.scheduleGuild(config.guildId, config.postingIntervalMinutes, config.targetChannelId, config.tagFilter);
                }
            }
            this.isInitialized = true;
            console.log(`[Random Content Scheduler] Initialized with ${activeConfigs.length} active configurations`);
        }
        catch (error) {
            console.error('[Random Content Scheduler] Failed to initialize:', error);
        }
    }
    /**
     * Schedule random content posting for a guild
     */
    async scheduleGuild(guildId, intervalMinutes, channelId, tagFilter) {
        try {
            // Remove existing job if any
            await this.unscheduleGuild(guildId);
            // Validate interval (minimum 5 minutes to prevent spam)
            if (intervalMinutes < 5) {
                console.warn(`[Random Content Scheduler] Invalid interval ${intervalMinutes} for guild ${guildId}, using minimum 5 minutes`);
                intervalMinutes = 5;
            }
            // Create cron expression for the interval
            const cronExpression = `*/${intervalMinutes} * * * *`;
            // Validate cron expression
            if (!node_cron_1.default.validate(cronExpression)) {
                console.error(`[Random Content Scheduler] Invalid cron expression: ${cronExpression}`);
                return;
            }
            // Create scheduled task
            const task = node_cron_1.default.schedule(cronExpression, async () => {
                await this.executeScheduledPost(guildId, channelId, tagFilter);
            }, {
                scheduled: false, // Don't start immediately
                timezone: 'UTC'
            });
            // Store the job
            this.scheduledJobs.set(guildId, {
                guildId,
                task,
                intervalMinutes
            });
            // Start the task
            task.start();
            console.log(`[Random Content Scheduler] Scheduled guild ${guildId} for posting every ${intervalMinutes} minutes`);
        }
        catch (error) {
            console.error(`[Random Content Scheduler] Failed to schedule guild ${guildId}:`, error);
        }
    }
    /**
     * Unschedule random content posting for a guild
     */
    async unscheduleGuild(guildId) {
        const existingJob = this.scheduledJobs.get(guildId);
        if (existingJob) {
            existingJob.task.stop();
            existingJob.task.destroy();
            this.scheduledJobs.delete(guildId);
            console.log(`[Random Content Scheduler] Unscheduled guild ${guildId}`);
        }
    }
    /**
     * Update schedule for a guild
     */
    async updateGuildSchedule(guildId, intervalMinutes, channelId, tagFilter) {
        await this.scheduleGuild(guildId, intervalMinutes, channelId, tagFilter);
    }
    /**
     * Execute a scheduled content post
     */
    async executeScheduledPost(guildId, channelId, tagFilter) {
        try {
            console.log(`[Random Content Scheduler] Executing scheduled post for guild ${guildId}`);
            const success = await (0, randomContentService_1.postRandomContent)(this.client, guildId, channelId, tagFilter);
            if (success) {
                console.log(`[Random Content Scheduler] Successfully posted content for guild ${guildId}`);
            }
            else {
                console.log(`[Random Content Scheduler] No content available for guild ${guildId}`);
            }
        }
        catch (error) {
            console.error(`[Random Content Scheduler] Failed to post content for guild ${guildId}:`, error);
            // If there are repeated failures, consider temporarily disabling the schedule
            await this.handleScheduleError(guildId, error);
        }
    }
    /**
     * Handle scheduling errors
     */
    async handleScheduleError(guildId, error) {
        try {
            // For now, just log the error. In the future, we could implement:
            // - Error counting and temporary disabling
            // - Notification to guild admins
            // - Automatic retry with backoff
            console.error(`[Random Content Scheduler] Handling error for guild ${guildId}:`, error);
            // If it's a permission error or channel not found, disable the schedule
            if (error.message?.includes('Missing Permissions') ||
                error.message?.includes('Unknown Channel') ||
                error.message?.includes('Invalid or inaccessible channel')) {
                console.log(`[Random Content Scheduler] Disabling schedule for guild ${guildId} due to channel/permission issues`);
                // Disable in database
                await RandomContentConfig_1.default.findOneAndUpdate({ guildId }, { enabled: false }, { new: true });
                // Remove from scheduler
                await this.unscheduleGuild(guildId);
            }
        }
        catch (handlerError) {
            console.error(`[Random Content Scheduler] Error in error handler for guild ${guildId}:`, handlerError);
        }
    }
    /**
     * Get status of all scheduled jobs
     */
    getScheduleStatus() {
        return Array.from(this.scheduledJobs.values()).map(job => ({
            guildId: job.guildId,
            intervalMinutes: job.intervalMinutes,
            isRunning: job.task.running
        }));
    }
    /**
     * Stop all scheduled jobs
     */
    async stopAll() {
        console.log('[Random Content Scheduler] Stopping all scheduled jobs...');
        for (const [guildId, job] of this.scheduledJobs) {
            job.task.stop();
            job.task.destroy();
        }
        this.scheduledJobs.clear();
        this.isInitialized = false;
        console.log('[Random Content Scheduler] All jobs stopped');
    }
    /**
     * Reload schedules from database
     */
    async reloadSchedules() {
        console.log('[Random Content Scheduler] Reloading schedules from database...');
        // Stop all current jobs
        await this.stopAll();
        // Reinitialize
        await this.initialize();
    }
}
exports.default = RandomContentScheduler;
