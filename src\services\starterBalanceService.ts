import { <PERSON><PERSON><PERSON>ber, Role } from 'discord.js';
import { StarterBalance, IStarterBalance } from '../models/StarterBalance';
import { adjustBalance } from './economyService';
import { DatabaseError } from '../utils/errorHandler';
import { createSuccessEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';

/**
 * Processes starter balance when a user receives a role
 */
export async function processStarterBalance(member: GuildMember, role: Role): Promise<boolean> {
    try {
        // Check if there's a starter balance rule for this role
        const starterBalanceRule = await StarterBalance.findOne({
            guildId: member.guild.id,
            roleId: role.id
        });

        if (!starterBalanceRule) {
            return false; // No starter balance rule for this role
        }

        // Grant the starter balance
        await adjustBalance(
            member.id,
            starterBalanceRule.amount,
            'starter_balance',
            `Starter balance for role: ${role.name}`,
            member.client,
            member.guild.id
        );

        console.log(`[Starter Balance] Granted ${starterBalanceRule.amount} PLC to ${member.displayName} for receiving role ${role.name}`);

        // Send DM to user notifying them of the starter balance
        try {
            const embed = createSuccessEmbed('Starter Balance Awarded!')
                .setDescription(
                    `${EMOJIS.SUCCESS.PARTY} **Welcome Bonus!**\n\n` +
                    `You have been awarded **${starterBalanceRule.amount} PLC** ` +
                    `for receiving the **${role.name}** role!\n\n` +
                    `${EMOJIS.ECONOMY.COINS} Use \`/balance\` to check your current balance and ` +
                    `\`/roles\` to see what you can purchase with your coins.`
                )
                .setColor(COLORS.SUCCESS)
                .setFooter({ text: 'Phalanx Order Economy System' });

            await member.send({ embeds: [embed] });
        } catch (dmError) {
            console.error(`[Starter Balance] Failed to send DM to ${member.displayName}:`, dmError);
            // Don't throw error for DM failure - the balance was still granted
        }

        return true;

    } catch (error) {
        console.error(`[Starter Balance] Failed to process starter balance for ${member.displayName} and role ${role.name}:`, error);
        throw new DatabaseError(`Failed to process starter balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Gets all starter balance rules for a guild
 */
export async function getStarterBalanceRules(guildId: string): Promise<IStarterBalance[]> {
    try {
        return await StarterBalance.find({ guildId }).sort({ roleName: 1 });
    } catch (error) {
        console.error(`[Starter Balance] Failed to get starter balance rules for guild ${guildId}:`, error);
        throw new DatabaseError(`Failed to retrieve starter balance rules: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Creates a new starter balance rule
 */
export async function createStarterBalanceRule(guildId: string, roleId: string, roleName: string, amount: number): Promise<IStarterBalance> {
    try {
        // Check if rule already exists
        const existingRule = await StarterBalance.findOne({ guildId, roleId });
        if (existingRule) {
            throw new DatabaseError(`Starter balance rule already exists for role ${roleName}`);
        }

        // Create new rule
        const newRule = new StarterBalance({
            guildId,
            roleId,
            roleName,
            amount
        });

        await newRule.save();
        console.log(`[Starter Balance] Created new rule: ${amount} PLC for role ${roleName} in guild ${guildId}`);

        return newRule;

    } catch (error) {
        if (error instanceof DatabaseError) {
            throw error;
        }
        console.error(`[Starter Balance] Failed to create starter balance rule:`, error);
        throw new DatabaseError(`Failed to create starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Updates an existing starter balance rule
 */
export async function updateStarterBalanceRule(guildId: string, roleId: string, newAmount: number): Promise<IStarterBalance | null> {
    try {
        const updatedRule = await StarterBalance.findOneAndUpdate(
            { guildId, roleId },
            { amount: newAmount },
            { new: true, runValidators: true }
        );

        if (updatedRule) {
            console.log(`[Starter Balance] Updated rule for role ${updatedRule.roleName}: ${newAmount} PLC`);
        }

        return updatedRule;

    } catch (error) {
        console.error(`[Starter Balance] Failed to update starter balance rule:`, error);
        throw new DatabaseError(`Failed to update starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Removes a starter balance rule
 */
export async function removeStarterBalanceRule(guildId: string, roleId: string): Promise<boolean> {
    try {
        const deletedRule = await StarterBalance.findOneAndDelete({ guildId, roleId });

        if (deletedRule) {
            console.log(`[Starter Balance] Removed rule for role ${deletedRule.roleName}`);
            return true;
        }

        return false;

    } catch (error) {
        console.error(`[Starter Balance] Failed to remove starter balance rule:`, error);
        throw new DatabaseError(`Failed to remove starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Checks if a starter balance rule exists for a specific role
 */
export async function hasStarterBalanceRule(guildId: string, roleId: string): Promise<boolean> {
    try {
        const rule = await StarterBalance.findOne({ guildId, roleId });
        return !!rule;
    } catch (error) {
        console.error(`[Starter Balance] Failed to check starter balance rule existence:`, error);
        return false;
    }
}
