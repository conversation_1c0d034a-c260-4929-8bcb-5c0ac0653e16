import { Client, GatewayIntentBits, Collection, Interaction, MessageReaction, User, PartialMessageReaction, PartialUser, GuildMember, PartialGuildMember } from 'discord.js';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import cron from 'node-cron';
import { processReactionReward } from './services/reactionRewardsService';
import { processTaxCollection } from './services/taxService';
import { processStarterBalance } from './services/starterBalanceService';
import { processJoinMessage, processRoleChangeMessage } from './services/automessageService';
import { UserCleanupService } from './services/userCleanupService';
import { EMOJIS } from './utils/embedBuilder';
import { handleCommandError, handleButtonError } from './utils/errorHandler';

dotenv.config();

mongoose.connect(process.env.MONGODB_URI as string)
  .then(async () => {
    console.log('Connected to MongoDB');

    // Database cleanup and initialization
    await initializeDatabase();
  })
  .catch((err) => console.error('MongoDB connection error:', err));

async function initializeDatabase() {
  try {
    console.log('Initializing database...');

    // Get the users collection
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('Database connection not established');
    }
    const usersCollection = db.collection('users');

    // Check for existing indexes
    const indexes = await usersCollection.indexes();
    console.log('Existing indexes:', indexes.map(idx => idx.name));

    // Remove old userId index if it exists
    try {
      await usersCollection.dropIndex('userId_1');
      console.log('Dropped old userId_1 index');
    } catch (error) {
      // Index might not exist, which is fine
      console.log('userId_1 index not found (this is expected)');
    }

    // Clean up any records with null discordId
    const deleteResult = await usersCollection.deleteMany({
      $or: [
        { discordId: null },
        { discordId: { $exists: false } },
        { userId: { $exists: true } } // Remove old schema records
      ]
    });

    if (deleteResult.deletedCount > 0) {
      console.log(`Cleaned up ${deleteResult.deletedCount} corrupted user records`);
    }

    // Ensure proper index on discordId
    await usersCollection.createIndex({ discordId: 1 }, { unique: true });
    console.log('Ensured discordId index exists');

    console.log('Database initialization complete');
  } catch (error) {
    console.error('Database initialization error:', error);
  }
}

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessageReactions
  ]
});

(client as any).commands = new Collection();

const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js') || file.endsWith('.ts'));

for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  const command = require(filePath);
  if (command.data && command.execute) {
    (client as any).commands.set(command.data.name, command);
  }
}

client.once('ready', () => {
  console.log(`Logged in as ${client.user?.tag}`);

  // Initialize tax collection cron job (runs every hour)
  cron.schedule('0 * * * *', async () => {
    console.log('[Tax Collection] Running scheduled tax collection check...');

    try {
      // Process tax collection for all guilds the bot is in
      for (const [guildId, guild] of client.guilds.cache) {
        try {
          const result = await processTaxCollection(client, guildId);
          if (result.totalProcessed > 0) {
            console.log(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
            if (result.errors.length > 0) {
              console.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
            }
          }
        } catch (error) {
          console.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
        }
      }
    } catch (error) {
      console.error('[Tax Collection] Cron job error:', error);
    }
  }, {
    timezone: "UTC"
  });

  console.log('[Tax Collection] Cron job initialized - running every hour');
});

client.on('interactionCreate', async (interaction: Interaction) => {
  if (interaction.isChatInputCommand()) {
    const command = (client as any).commands.get(interaction.commandName);
    if (!command) return;
    try {
      await command.execute(interaction);
    } catch (error) {
      await handleCommandError(interaction, error);
    }
  } else if (interaction.isButton()) {
    // Handle button interactions
    try {
      const { customId } = interaction;

      if (customId === 'quick_balance') {
        const balanceCommand = (client as any).commands.get('balance');
        if (balanceCommand) {
          await balanceCommand.execute(interaction);
        }
      } else if (customId === 'quick_leaderboard') {
        const leaderboardCommand = (client as any).commands.get('leaderboard');
        if (leaderboardCommand) {
          await leaderboardCommand.execute(interaction);
        }
      } else if (customId === 'quick_roles') {
        const rolesCommand = (client as any).commands.get('roles');
        if (rolesCommand) {
          await rolesCommand.execute(interaction);
        }
      } else if (customId.startsWith('buy_role_')) {
        // Handle role achievement info buttons
        const roleId = customId.replace('buy_role_', '');
        await interaction.reply({
          content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
          ephemeral: true
        });
      } else if (customId === 'announce_confirm') {
        // Handle announcement confirmation
        const pendingAnnouncements = (global as any).pendingAnnouncements;
        if (!pendingAnnouncements) {
          await interaction.reply({
            content: 'No pending announcements found. Please try the command again.',
            ephemeral: true
          });
          return;
        }

        const originalInteractionId = interaction.message?.interaction?.id;
        const announcementData = pendingAnnouncements.get(originalInteractionId);

        if (!announcementData) {
          await interaction.reply({
            content: 'Announcement data not found or expired. Please try the command again.',
            ephemeral: true
          });
          return;
        }

        // Clean up the pending announcement
        pendingAnnouncements.delete(originalInteractionId);

        // Process the announcement
        await interaction.deferUpdate();
        const announceModule = require('./commands/announce');
        await announceModule.processAnnouncement(interaction, announcementData);
      } else if (customId === 'announce_cancel') {
        // Handle announcement cancellation
        const pendingAnnouncements = (global as any).pendingAnnouncements;
        const originalInteractionId = interaction.message?.interaction?.id;

        if (pendingAnnouncements && originalInteractionId) {
          pendingAnnouncements.delete(originalInteractionId);
        }

        await interaction.update({
          content: `${EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
          embeds: [],
          components: []
        });
      } else if (customId.startsWith('help_')) {
        // Handle help command buttons
        const commandName = customId.replace('help_', '');

        // Commands that can be executed directly
        const instantTriggerCommands = ['balance', 'roles', 'leaderboard', 'history'];

        if (instantTriggerCommands.includes(commandName)) {
          // Execute the command directly
          const command = (client as any).commands.get(commandName);
          if (command) {
            await command.execute(interaction);
          } else {
            await interaction.reply({
              content: `Command "${commandName}" not found.`,
              ephemeral: true
            });
          }
        } else {
          // For other commands, show usage information
          const commandDescriptions: Record<string, string> = {
            'pay': 'Transfer coins to another user. Usage: `/pay @user amount`',
            'addrole': '**[Admin Only]** Add a role achievement. Usage: `/addrole @role price`',
            'editrole': '**[Admin Only]** Edit a role achievement. Usage: `/editrole role_name new_price`',
            'removerole': '**[Admin Only]** Remove a role achievement. Usage: `/removerole role_name`',
            'give': '**[Admin Only]** Give coins to a user. Usage: `/give @user amount`',
            'fine': '**[Admin Only]** Remove coins from a user. Usage: `/fine @user amount`',
            'tax': '**[Admin Only]** Configure automatic taxation system. Usage: `/tax status:on/off frequency:weeks amount:plc role:@role`',
            'starterbalance': '**[Admin Only]** Manage starter balance rules. Usage: `/starterbalance action:add/edit/remove/list role:@role amount:plc`',
            'incomecredentials': '**[Admin Only]** Customize income earning guide text displayed in /help command. Supports line breaks (\\n) and formatting. Usage: `/incomecredentials text:"Your custom income guide text"`',
            'automessage': '**[Admin Only]** Manage automated messages for server events. Usage: `/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!`',
            'placeholders': 'View available placeholders for automated messages. Usage: `/placeholders`',
            'testcleanup': '**[Admin Only]** Test user data cleanup functionality. Usage: `/testcleanup user:@user action:check/simulate`'
          };

          const description = commandDescriptions[commandName] || `Use the /${commandName} command.`;

          await interaction.reply({
            content: `**/${commandName}**\n${description}`,
            ephemeral: true
          });
        }
      } else {
        await interaction.reply({
          content: 'This button interaction is not yet implemented.',
          ephemeral: true
        });
      }
    } catch (error) {
      await handleButtonError(interaction, error);
    }
  }
});

// Reaction rewards event handler
client.on('messageReactionAdd', async (reaction: MessageReaction | PartialMessageReaction, user: User | PartialUser) => {
  try {
    // Handle partial reactions and users
    if (reaction.partial) {
      try {
        await reaction.fetch();
      } catch (error) {
        console.error('[Reaction Rewards] Failed to fetch reaction:', error);
        return;
      }
    }

    if (user.partial) {
      try {
        await user.fetch();
      } catch (error) {
        console.error('[Reaction Rewards] Failed to fetch user:', error);
        return;
      }
    }

    // Process the reaction reward
    await processReactionReward(reaction as MessageReaction, user as User);
  } catch (error) {
    console.error('[Reaction Rewards] Error in messageReactionAdd handler:', error);
  }
});

// Guild member add event handler for automated messages
client.on('guildMemberAdd', async (member: GuildMember) => {
  try {
    // Process join messages
    const joinResult = await processJoinMessage(member);
    if (joinResult.sent) {
      console.log(`[AutoMessage] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
    }
    if (joinResult.errors.length > 0) {
      console.error(`[AutoMessage] Errors processing join messages for ${member.displayName}:`, joinResult.errors);
    }

    // Note: Starter balance is processed in guildMemberUpdate when roles are added
  } catch (error) {
    console.error('[AutoMessage] Error in guildMemberAdd handler:', error);
  }
});

// Guild member remove event handler for user data cleanup
client.on('guildMemberRemove', async (member: GuildMember | PartialGuildMember) => {
  try {
    // Handle partial members
    if (member.partial) {
      try {
        await member.fetch();
      } catch (error) {
        console.error('[User Cleanup] Failed to fetch member data:', error);
        // Continue with cleanup using available data
      }
    }

    const userId = member.user?.id;
    const guildName = member.guild?.name || 'Unknown Guild';
    const displayName = member.displayName || member.user?.username || 'Unknown User';

    if (!userId) {
      console.error('[User Cleanup] No user ID available for member who left');
      return;
    }

    // Check if we have user data before attempting cleanup
    const userData = await UserCleanupService.checkUserData(userId);
    const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;

    if (!hasData) {
      console.log(`[User Cleanup] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
      return;
    }

    console.log(`[User Cleanup] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());

    // Perform cleanup - we need to cast to GuildMember for the cleanup service
    const cleanupResult = await UserCleanupService.cleanupUserData(member as GuildMember);

    if (cleanupResult.success) {
      const removedItems = [];
      if (cleanupResult.userDataRemoved) removedItems.push('user balance');
      if (cleanupResult.transactionsRemoved > 0) removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
      if (cleanupResult.reactionRewardsRemoved > 0) removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);

      console.log(`[User Cleanup] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
    } else {
      console.error(`[User Cleanup] Failed to clean up data for ${displayName}:`, cleanupResult.errors);
    }
  } catch (error) {
    console.error(`[User Cleanup] Error in guildMemberRemove handler:`, error);
  }
});

// Guild member update event handler for starter balance and role change messages
client.on('guildMemberUpdate', async (oldMember: GuildMember | PartialGuildMember, newMember: GuildMember) => {
  try {
    // Handle partial members
    if (oldMember.partial) {
      try {
        await oldMember.fetch();
      } catch (error) {
        console.error('[Member Update] Failed to fetch old member:', error);
        return;
      }
    }

    // Check for role changes
    const addedRoles = newMember.roles.cache.filter(role => !oldMember.roles.cache.has(role.id));
    const removedRoles = oldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));

    // Process added roles
    if (addedRoles.size > 0) {
      for (const [roleId, role] of addedRoles) {
        try {
          // Process starter balance
          const granted = await processStarterBalance(newMember, role);
          if (granted) {
            console.log(`[Starter Balance] Granted starter balance to ${newMember.displayName} for role ${role.name}`);
          }

          // Process role add messages
          const roleAddResult = await processRoleChangeMessage(newMember, role, 'role_add');
          if (roleAddResult.sent) {
            console.log(`[AutoMessage] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${newMember.displayName} for role ${role.name}`);
          }
          if (roleAddResult.errors.length > 0) {
            console.error(`[AutoMessage] Errors processing role add messages for ${newMember.displayName}:`, roleAddResult.errors);
          }
        } catch (error) {
          console.error(`[Member Update] Failed to process role add for ${newMember.displayName} and role ${role.name}:`, error);
        }
      }
    }

    // Process removed roles
    if (removedRoles.size > 0) {
      for (const [roleId, role] of removedRoles) {
        try {
          // Process role remove messages
          const roleRemoveResult = await processRoleChangeMessage(newMember, role, 'role_remove');
          if (roleRemoveResult.sent) {
            console.log(`[AutoMessage] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${newMember.displayName} for role ${role.name}`);
          }
          if (roleRemoveResult.errors.length > 0) {
            console.error(`[AutoMessage] Errors processing role remove messages for ${newMember.displayName}:`, roleRemoveResult.errors);
          }
        } catch (error) {
          console.error(`[Member Update] Failed to process role remove for ${newMember.displayName} and role ${role.name}:`, error);
        }
      }
    }
  } catch (error) {
    console.error('[Member Update] Error in guildMemberUpdate handler:', error);
  }
});

client.login(process.env.BOT_TOKEN);
