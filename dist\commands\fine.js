"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const economyService_1 = require("../services/economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('fine')
        .setDescription('Fine (remove coins from) a user (admin only)')
        .addUserOption(option => option.setName('user').setDescription('User to fine').setRequired(true))
        .addIntegerOption(option => option.setName('amount').setDescription('Amount to fine').setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        // Input validation
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than zero.');
        }
        if (targetUser.bot) {
            throw new errorHandler_1.ValidationError('You cannot fine a bot.');
        }
        // adjustBalance already handles user creation with upsert, no need for manual user creation
        await (0, economyService_1.adjustBalance)(targetUser.id, -amount, 'fine', `Fined by admin ${interaction.user.tag}`, interaction.client, interaction.guild?.id);
        // Create rich admin warning embed for fine
        const embed = (0, embedBuilder_1.createAdminEmbed)('Fine Issued Successfully')
            .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SCALES} **Administrative Penalty Applied**\n\n` +
            `${(0, embedBuilder_1.formatCoins)(amount)} has been deducted from **${targetUser.displayName}**'s account.`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Administrator`,
            value: `**${interaction.user.displayName}**`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Target User`,
            value: `**${targetUser.displayName}**`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.DOLLAR} Fine Amount`,
            value: (0, embedBuilder_1.formatCoins)(amount),
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Action Details`,
            value: `Administrative fine issued by ${interaction.user.displayName}`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Action Time`,
            value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
            inline: false
        })
            .setColor(embedBuilder_1.COLORS.WARNING)
            .setFooter({
            text: 'Administrative action logged in transaction history'
        });
        // Add admin's avatar to embed
        (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
        await interaction.reply({
            embeds: [embed],
            ephemeral: false
        });
    })
};
