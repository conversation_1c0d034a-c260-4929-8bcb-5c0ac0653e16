"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const economyService_1 = require("../services/economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('give')
        .setDescription('Give coins to a user (admin only)')
        .addUserOption(option => option.setName('user').setDescription('User to give coins to').setRequired(true))
        .addIntegerOption(option => option.setName('amount').setDescription('Amount to give').setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        // Input validation
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than zero.');
        }
        if (targetUser.bot) {
            throw new errorHandler_1.ValidationError('You cannot give coins to a bot.');
        }
        // adjustBalance already handles user creation with upsert, no need for ensureUser
        await (0, economyService_1.adjustBalance)(targetUser.id, amount, 'give', `Given by admin ${interaction.user.tag}`, interaction.client, interaction.guild?.id);
        // Create rich admin success embed
        const embed = (0, embedBuilder_1.createSuccessEmbed)('Coins Awarded Successfully!')
            .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.HAMMER} **Administrative Action Completed**\n\n` +
            `${(0, embedBuilder_1.formatCoins)(amount)} has been awarded to **${targetUser.displayName}**!`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.ADMIN.KEY} Administrator`,
            value: `**${interaction.user.displayName}**`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} Recipient`,
            value: `**${targetUser.displayName}**`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount Awarded`,
            value: (0, embedBuilder_1.formatCoins)(amount),
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Transaction Details`,
            value: `Admin reward issued by ${interaction.user.displayName}`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Transaction Time`,
            value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
            inline: false
        })
            .setFooter({
            text: 'Administrative action logged in transaction history'
        });
        // Add admin's avatar to embed
        (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
        await interaction.reply({
            embeds: [embed],
            ephemeral: false
        });
    })
};
