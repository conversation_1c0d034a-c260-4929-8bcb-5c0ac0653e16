# 🧹 User Data Cleanup System

The Economy Bot includes an automatic user data cleanup system that removes user data when members leave the server, ensuring compliance with data privacy practices and maintaining a clean database.

## 🚀 How It Works

### Automatic Cleanup Process

When a member leaves the server, the bot automatically:

1. **Grace Period** - Waits 5 seconds to prevent accidental cleanup if the user rejoins quickly
2. **Data Check** - Verifies what user data exists in the database
3. **Cleanup Execution** - Removes all user-related data in a database transaction
4. **Logging** - Records the cleanup results with detailed information
5. **Error Handling** - Ensures the process doesn't crash the bot if issues occur

### What Data Gets Cleaned Up

The system removes the following user-specific data:

- **User Balance Records** - PLC balance and account information
- **Transaction History** - All transaction records (pay, role_achievement, give, fine, reaction, tax, starter_balance)
- **Reaction Rewards** - All reaction reward records for the user

### What Data Is Preserved

The following server-specific configurations are **NOT** affected:

- **Guild Configurations** - Welcome settings, tax settings, starter balance configurations
- **Role Achievements** - Available role achievements
- **Monetized Channels** - Channel monetization settings
- **Server Templates** - Welcome message templates

## 🔧 Technical Implementation

### Database Collections Affected

1. **Users Collection** - `{ discordId: string, balance: number }`
2. **Transactions Collection** - `{ discordId: string, type: string, amount: number, ... }`
3. **ReactionRewards Collection** - `{ userId: string, messageId: string, ... }`

### Safety Features

- **Grace Period** - 5-second delay prevents accidental cleanup
- **Database Transactions** - Ensures all-or-nothing cleanup (rollback on failure)
- **Partial Member Handling** - Works even with limited Discord API data
- **Error Isolation** - Cleanup failures don't crash the bot
- **Comprehensive Logging** - Detailed logs for monitoring and debugging

### Performance Considerations

- **Efficient Queries** - Uses indexed fields for fast lookups
- **Timeout Protection** - 30-second timeout prevents hanging operations
- **Minimal Impact** - Cleanup runs asynchronously without blocking other bot functions

## 🧪 Testing & Verification

### Test Command

Use the `/testcleanup` command to verify the cleanup system:

```
/testcleanup user:@username action:check
```
Shows what data exists for a user that would be cleaned up.

```
/testcleanup user:@username action:simulate
```
Simulates the cleanup process without actually removing data.

### Example Test Output

```
User Data Check for @TestUser

✅ User Balance Record: Found
✅ Transaction History: 15 records  
✅ Reaction Rewards: 8 records

Cleanup Impact: If this user leaves the server, 24 database records 
would be automatically removed after a 5-second grace period.
```

## 📊 Monitoring & Logs

### Console Logging

The system provides detailed console logs:

```
[User Cleanup] User TestUser left ServerName - found data: balance 15 transactions 8 reaction rewards
[User Cleanup] Successfully cleaned up data for TestUser: user balance, 15 transactions, 8 reaction rewards (1247ms)
```

### Error Logging

Failed cleanups are logged with full error details:

```
[User Cleanup] Failed to clean up data for TestUser: Database connection timeout
```

### Success Metrics

- **Records Removed** - Count of each data type cleaned up
- **Execution Time** - How long the cleanup took
- **Success Rate** - Whether the cleanup completed successfully

## 🔒 Privacy & Compliance

### Data Privacy Benefits

- **Automatic Removal** - No manual intervention required
- **Complete Cleanup** - All user data is removed, not just marked as deleted
- **Immediate Effect** - Cleanup happens within seconds of member leaving
- **Audit Trail** - Full logging for compliance verification

### GDPR Compliance

The cleanup system helps with GDPR compliance by:

- **Right to be Forgotten** - Automatic data removal when users leave
- **Data Minimization** - Only retains data for active members
- **Purpose Limitation** - Removes data when no longer needed for bot functionality

## ⚙️ Configuration

### Default Settings

- **Grace Period**: 5 seconds
- **Timeout**: 30 seconds
- **Enabled**: Automatic (no configuration needed)

### Customization

The cleanup system is designed to work automatically without configuration. If you need to modify the behavior:

1. **Grace Period** - Modify `GRACE_PERIOD_MS` in `UserCleanupService`
2. **Timeout** - Modify `CLEANUP_TIMEOUT_MS` in `UserCleanupService`
3. **Logging Level** - Adjust console.log statements as needed

## 🚨 Troubleshooting

### Common Issues

1. **Cleanup Not Running**
   - Check console logs for error messages
   - Verify bot has database permissions
   - Ensure MongoDB connection is stable

2. **Partial Cleanup**
   - Database transaction ensures all-or-nothing cleanup
   - Check logs for specific error details
   - Failed cleanups will be logged with full error information

3. **Performance Issues**
   - Cleanup runs asynchronously and shouldn't impact bot performance
   - Large amounts of user data may take longer to clean up
   - Monitor execution times in logs

### Support

For issues with the cleanup system:

1. Check console logs for detailed error messages
2. Use `/testcleanup` to verify system functionality
3. Monitor database performance during cleanup operations
4. Review error logs for patterns or recurring issues

## 🔄 Future Enhancements

Potential improvements for the cleanup system:

- **Bulk Cleanup** - Clean up data for multiple users at once
- **Scheduled Cleanup** - Periodic cleanup of orphaned data
- **Cleanup Statistics** - Dashboard showing cleanup metrics
- **Configurable Grace Period** - Admin-configurable delay settings
- **Backup Before Cleanup** - Optional data backup before removal
