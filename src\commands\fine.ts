import { Slash<PERSON>ommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { adjustBalance } from '../services/economyService';
import { withErrorHandler, ValidationError, PermissionError } from '../utils/errorHandler';
import { createAdminEmbed, addUserInfo, formatCoins, EMOJIS, COLORS } from '../utils/embedBuilder';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('fine')
        .setDescription('Fine (remove coins from) a user (admin only)')
        .addUserOption(option => option.setName('user').setDescription('User to fine').setRequired(true))
        .addIntegerOption(option => option.setName('amount').setDescription('Amount to fine').setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Permission check
        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError();
        }

        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);

        // Input validation
        if (amount <= 0) {
            throw new ValidationError('Amount must be greater than zero.');
        }
        if (targetUser.bot) {
            throw new ValidationError('You cannot fine a bot.');
        }

        // adjustBalance already handles user creation with upsert, no need for manual user creation
        await adjustBalance(
            targetUser.id,
            -amount,
            'fine',
            `Fined by admin ${interaction.user.tag}`,
            interaction.client,
            interaction.guild?.id
        );

        // Create rich admin warning embed for fine
        const embed = createAdminEmbed('Fine Issued Successfully')
            .setDescription(
                `${EMOJIS.ADMIN.SCALES} **Administrative Penalty Applied**\n\n` +
                `${formatCoins(amount)} has been deducted from **${targetUser.displayName}**'s account.`
            )
            .addFields(
                {
                    name: `${EMOJIS.ADMIN.HAMMER} Administrator`,
                    value: `**${interaction.user.displayName}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.ADMIN.WARNING} Target User`,
                    value: `**${targetUser.displayName}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.ECONOMY.DOLLAR} Fine Amount`,
                    value: formatCoins(amount),
                    inline: true
                },
                {
                    name: `${EMOJIS.MISC.SCROLL} Action Details`,
                    value: `Administrative fine issued by ${interaction.user.displayName}`,
                    inline: false
                },
                {
                    name: `${EMOJIS.MISC.CLOCK} Action Time`,
                    value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: false
                }
            )
            .setColor(COLORS.WARNING)
            .setFooter({
                text: 'Administrative action logged in transaction history'
            });

        // Add admin's avatar to embed
        addUserInfo(embed, interaction.user);

        await interaction.reply({
            embeds: [embed],
            ephemeral: false
        });
    })
};
