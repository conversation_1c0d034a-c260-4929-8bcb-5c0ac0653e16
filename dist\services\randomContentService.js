"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setGlobalScheduler = setGlobalScheduler;
exports.getOrCreateConfig = getOrCreateConfig;
exports.updateConfig = updateConfig;
exports.selectRandomContent = selectRandomContent;
exports.createContentEmbed = createContentEmbed;
exports.postRandomContent = postRandomContent;
exports.getContentStats = getContentStats;
const ContentSubmission_1 = __importDefault(require("../models/ContentSubmission"));
const RandomContentConfig_1 = __importDefault(require("../models/RandomContentConfig"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
// Global reference to scheduler (will be set from index.ts)
let globalScheduler = null;
function setGlobalScheduler(scheduler) {
    globalScheduler = scheduler;
}
function logDatabaseOperation(operation, details) {
    console.log(`[Random Content] ${operation}:`, JSON.stringify(details, null, 2));
}
/**
 * Get or create random content configuration for a guild
 */
async function getOrCreateConfig(guildId) {
    try {
        let config = await RandomContentConfig_1.default.findOne({ guildId });
        if (!config) {
            config = await RandomContentConfig_1.default.create({
                guildId,
                postingIntervalMinutes: 60,
                enabled: false
            });
            logDatabaseOperation('Config Created', { guildId });
        }
        return config;
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to get or create random content configuration', error.message);
    }
}
/**
 * Update random content configuration
 */
async function updateConfig(guildId, updates) {
    try {
        const config = await RandomContentConfig_1.default.findOneAndUpdate({ guildId }, { ...updates, updatedAt: new Date() }, { new: true, upsert: true });
        logDatabaseOperation('Config Updated', { guildId, updates });
        // Update scheduler if available
        if (globalScheduler && config) {
            if (config.enabled && config.targetChannelId) {
                await globalScheduler.scheduleGuild(guildId, config.postingIntervalMinutes, config.targetChannelId, config.tagFilter);
            }
            else {
                await globalScheduler.unscheduleGuild(guildId);
            }
        }
        return config;
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to update random content configuration', error.message);
    }
}
/**
 * Select random content using weighted algorithm
 */
async function selectRandomContent(guildId, tagFilter) {
    try {
        const query = { guildId, isActive: true };
        // Apply tag filter if provided
        if (tagFilter && tagFilter.length > 0) {
            query.tags = { $in: tagFilter };
        }
        // Get all eligible content
        const allContent = await ContentSubmission_1.default.find(query).lean();
        if (allContent.length === 0) {
            return null;
        }
        // Calculate weights (favor less-used content)
        const weightedContent = allContent.map(content => ({
            content,
            weight: 1 / (content.usageCount + 1)
        }));
        // Calculate total weight
        const totalWeight = weightedContent.reduce((sum, item) => sum + item.weight, 0);
        // Select random content based on weights
        let random = Math.random() * totalWeight;
        let selectedContent = null;
        for (const item of weightedContent) {
            random -= item.weight;
            if (random <= 0) {
                selectedContent = item.content;
                break;
            }
        }
        // Fallback to first item if something goes wrong
        if (!selectedContent) {
            selectedContent = allContent[0];
        }
        // Update usage statistics
        await ContentSubmission_1.default.findByIdAndUpdate(selectedContent._id, {
            $inc: { usageCount: 1 },
            lastUsedAt: new Date()
        });
        logDatabaseOperation('Content Selected', {
            contentId: selectedContent._id,
            contentType: selectedContent.contentType,
            usageCount: selectedContent.usageCount + 1
        });
        return selectedContent;
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to select random content', error.message);
    }
}
/**
 * Create embed for content posting
 */
function createContentEmbed(content, client) {
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.MISC.SPARKLES} Random Content`, undefined, embedBuilder_1.COLORS.PRIMARY);
    // Add text content if available
    if (content.contentText) {
        embed.setDescription(content.contentText);
    }
    // Add image if available
    if (content.contentAttachments.length > 0) {
        const imageUrl = content.contentAttachments.find(url => /\.(jpg|jpeg|png|gif|webp)(\?|$)/i.test(url));
        if (imageUrl) {
            embed.setImage(imageUrl);
        }
    }
    // Add URLs as fields
    if (content.contentUrls.length > 0) {
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.LINK} Links`,
            value: content.contentUrls.slice(0, 5).join('\n'),
            inline: false
        });
    }
    // Add tags if available
    if (content.tags && content.tags.length > 0) {
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.TAG} Tags`,
            value: content.tags.map(tag => `#${tag}`).join(' '),
            inline: false
        });
    }
    // Add footer with usage stats
    embed.setFooter({
        text: `Used ${content.usageCount} time${content.usageCount !== 1 ? 's' : ''} • Content ID: ${content._id}`
    });
    return embed;
}
/**
 * Post random content to a channel
 */
async function postRandomContent(client, guildId, channelId, tagFilter) {
    try {
        const channel = await client.channels.fetch(channelId);
        if (!channel || !channel.isTextBased()) {
            throw new Error('Invalid or inaccessible channel');
        }
        // Check bot permissions
        const permissions = channel.permissionsFor(client.user);
        if (!permissions?.has(['SendMessages', 'EmbedLinks'])) {
            throw new Error('Bot lacks necessary permissions in target channel');
        }
        // Select random content
        const content = await selectRandomContent(guildId, tagFilter);
        if (!content) {
            logDatabaseOperation('No Content Available', { guildId, tagFilter });
            return false;
        }
        // Create and send embed
        const embed = createContentEmbed(content, client);
        // Send content
        const messageOptions = { embeds: [embed] };
        // Add additional attachments if any
        if (content.contentAttachments.length > 1) {
            const additionalAttachments = content.contentAttachments.slice(1, 5); // Max 4 additional
            if (additionalAttachments.length > 0) {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.ATTACHMENT} Additional Attachments`,
                    value: additionalAttachments.join('\n'),
                    inline: false
                });
            }
        }
        await channel.send(messageOptions);
        logDatabaseOperation('Content Posted', {
            guildId,
            channelId,
            contentId: content._id,
            contentType: content.contentType
        });
        return true;
    }
    catch (error) {
        logDatabaseOperation('Content Posting Error', {
            guildId,
            channelId,
            error: error.message
        });
        throw new errorHandler_1.DatabaseError('Failed to post random content', error.message);
    }
}
/**
 * Get content statistics for a guild
 */
async function getContentStats(guildId, userId) {
    try {
        const baseQuery = { guildId, isActive: true };
        const userQuery = userId ? { ...baseQuery, userId } : baseQuery;
        const [totalSubmissions, activeSubmissions, userSubmissions, contentByType, allContent] = await Promise.all([
            ContentSubmission_1.default.countDocuments({ guildId }),
            ContentSubmission_1.default.countDocuments(baseQuery),
            userId ? ContentSubmission_1.default.countDocuments(userQuery) : Promise.resolve(undefined),
            ContentSubmission_1.default.aggregate([
                { $match: baseQuery },
                { $group: { _id: '$contentType', count: { $sum: 1 } } }
            ]),
            ContentSubmission_1.default.find(baseQuery, 'usageCount tags').lean()
        ]);
        // Calculate total usage
        const totalUsage = allContent.reduce((sum, content) => sum + content.usageCount, 0);
        // Calculate content by type
        const contentTypeStats = {};
        contentByType.forEach((item) => {
            contentTypeStats[item._id] = item.count;
        });
        // Calculate top tags
        const tagCounts = {};
        allContent.forEach(content => {
            if (content.tags) {
                content.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });
        const topTags = Object.entries(tagCounts)
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        return {
            totalSubmissions,
            activeSubmissions,
            totalUsage,
            userSubmissions,
            contentByType: contentTypeStats,
            topTags
        };
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to get content statistics', error.message);
    }
}
