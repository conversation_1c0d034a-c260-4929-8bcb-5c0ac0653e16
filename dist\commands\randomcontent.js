"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const randomContentService_1 = require("../services/randomContentService");
const contentSubmissionService_1 = require("../services/contentSubmissionService");
const contentUtils_1 = require("../utils/contentUtils");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('randomcontent')
        .setDescription('Manage the random content system')
        .addSubcommand(subcommand => subcommand
        .setName('setup')
        .setDescription('Configure random content posting (Admin only)')
        .addChannelOption(option => option
        .setName('channel')
        .setDescription('Channel where random content will be posted')
        .addChannelTypes(discord_js_1.ChannelType.GuildText)
        .setRequired(true))
        .addIntegerOption(option => option
        .setName('interval')
        .setDescription('Posting interval in minutes (5-10080)')
        .setMinValue(5)
        .setMaxValue(10080)
        .setRequired(false)))
        .addSubcommand(subcommand => subcommand
        .setName('toggle')
        .setDescription('Enable or disable automatic posting (Admin only)')
        .addBooleanOption(option => option
        .setName('enabled')
        .setDescription('Whether to enable automatic posting')
        .setRequired(true)))
        .addSubcommand(subcommand => subcommand
        .setName('settings')
        .setDescription('View current configuration (Admin only)'))
        .addSubcommand(subcommand => subcommand
        .setName('filter')
        .setDescription('Manage tag filters for automatic posting (Admin only)')
        .addStringOption(option => option
        .setName('action')
        .setDescription('Filter action')
        .setRequired(true)
        .addChoices({ name: 'Add tags', value: 'add' }, { name: 'Remove tags', value: 'remove' }, { name: 'Clear all', value: 'clear' }, { name: 'View current', value: 'view' }))
        .addStringOption(option => option
        .setName('tags')
        .setDescription('Space-separated list of tags (for add/remove actions)')
        .setRequired(false)))
        .addSubcommand(subcommand => subcommand
        .setName('post')
        .setDescription('Manually post random content')
        .addStringOption(option => option
        .setName('tag')
        .setDescription('Filter by specific tag')
        .setRequired(false)))
        .addSubcommand(subcommand => subcommand
        .setName('list')
        .setDescription('View stored content')
        .addStringOption(option => option
        .setName('tag')
        .setDescription('Filter by tag')
        .setRequired(false))
        .addUserOption(option => option
        .setName('user')
        .setDescription('Filter by user (Admin only)')
        .setRequired(false))
        .addIntegerOption(option => option
        .setName('page')
        .setDescription('Page number')
        .setMinValue(1)
        .setRequired(false)))
        .addSubcommand(subcommand => subcommand
        .setName('delete')
        .setDescription('Delete content by ID')
        .addStringOption(option => option
        .setName('content_id')
        .setDescription('Content ID to delete')
        .setRequired(true)))
        .addSubcommand(subcommand => subcommand
        .setName('stats')
        .setDescription('View content statistics')
        .addUserOption(option => option
        .setName('user')
        .setDescription('View stats for specific user (Admin only)')
        .setRequired(false))),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        const userId = interaction.user.id;
        // Check admin permissions for admin-only commands
        const isAdmin = interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.ManageGuild) || false;
        const adminCommands = ['setup', 'toggle', 'settings', 'filter'];
        if (adminCommands.includes(subcommand) && !isAdmin) {
            throw new errorHandler_1.PermissionError('You need Manage Guild permission to use this command');
        }
        switch (subcommand) {
            case 'setup': {
                const channel = interaction.options.getChannel('channel', true);
                const interval = interaction.options.getInteger('interval') || 60;
                // Validate channel permissions
                const permissions = channel.permissionsFor(interaction.client.user);
                if (!permissions?.has(['SendMessages', 'EmbedLinks'])) {
                    throw new errorHandler_1.ValidationError('Bot lacks necessary permissions in the specified channel');
                }
                await (0, randomContentService_1.updateConfig)(guildId, {
                    targetChannelId: channel.id,
                    postingIntervalMinutes: interval
                });
                const embed = (0, embedBuilder_1.createSuccessEmbed)('Random Content Setup Complete', `Random content will be posted in ${channel} every ${interval} minutes when enabled.`);
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.GUILD} Channel`,
                    value: `${channel}`,
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Interval`,
                    value: `${interval} minutes`,
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Next Step`,
                    value: 'Use `/randomcontent toggle enabled:true` to start automatic posting',
                    inline: false
                });
                await interaction.reply({ embeds: [embed] });
                break;
            }
            case 'toggle': {
                const enabled = interaction.options.getBoolean('enabled', true);
                const config = await (0, randomContentService_1.getOrCreateConfig)(guildId);
                if (enabled && !config.targetChannelId) {
                    throw new errorHandler_1.ValidationError('Please set up a target channel first using `/randomcontent setup`');
                }
                await (0, randomContentService_1.updateConfig)(guildId, { enabled });
                const embed = (0, embedBuilder_1.createSuccessEmbed)(`Random Content ${enabled ? 'Enabled' : 'Disabled'}`, enabled
                    ? `Automatic posting is now active. Content will be posted every ${config.postingIntervalMinutes} minutes.`
                    : 'Automatic posting has been disabled.');
                if (enabled && config.targetChannelId) {
                    const channel = await interaction.client.channels.fetch(config.targetChannelId);
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.MISC.GUILD} Target Channel`,
                        value: `${channel}`,
                        inline: true
                    });
                }
                await interaction.reply({ embeds: [embed] });
                break;
            }
            case 'settings': {
                const config = await (0, randomContentService_1.getOrCreateConfig)(guildId);
                const stats = await (0, randomContentService_1.getContentStats)(guildId);
                const embed = (0, embedBuilder_1.createAdminEmbed)('Random Content Settings');
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Status`,
                    value: config.enabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Interval`,
                    value: `${config.postingIntervalMinutes} minutes`,
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.GUILD} Target Channel`,
                    value: config.targetChannelId ? `<#${config.targetChannelId}>` : 'Not set',
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.TAG} Tag Filters`,
                    value: config.tagFilter && config.tagFilter.length > 0
                        ? config.tagFilter.map(tag => `#${tag}`).join(' ')
                        : 'None (all content)',
                    inline: false
                }, {
                    name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Content Statistics`,
                    value: [
                        `📊 Total: ${stats.totalSubmissions}`,
                        `✅ Active: ${stats.activeSubmissions}`,
                        `🎯 Total Usage: ${stats.totalUsage}`
                    ].join('\n'),
                    inline: false
                });
                await interaction.reply({ embeds: [embed] });
                break;
            }
            case 'filter': {
                const action = interaction.options.getString('action', true);
                const tagsInput = interaction.options.getString('tags');
                const config = await (0, randomContentService_1.getOrCreateConfig)(guildId);
                let tags = [];
                if (tagsInput && ['add', 'remove'].includes(action)) {
                    tags = tagsInput.split(/\s+/).map(tag => tag.toLowerCase()).filter(contentUtils_1.validateTag);
                    if (tags.length === 0) {
                        throw new errorHandler_1.ValidationError('No valid tags provided. Tags must be alphanumeric with underscores/hyphens.');
                    }
                }
                let newTagFilter = config.tagFilter || [];
                switch (action) {
                    case 'add':
                        newTagFilter = [...new Set([...newTagFilter, ...tags])];
                        break;
                    case 'remove':
                        newTagFilter = newTagFilter.filter(tag => !tags.includes(tag));
                        break;
                    case 'clear':
                        newTagFilter = [];
                        break;
                    case 'view':
                        // Just display current filters
                        break;
                }
                if (action !== 'view') {
                    await (0, randomContentService_1.updateConfig)(guildId, { tagFilter: newTagFilter });
                }
                const embed = (0, embedBuilder_1.createSuccessEmbed)('Tag Filter Management', action === 'view' ? 'Current tag filters:' : `Tag filters ${action}ed successfully.`);
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.TAG} Current Filters`,
                    value: newTagFilter.length > 0
                        ? newTagFilter.map(tag => `#${tag}`).join(' ')
                        : 'None (all content will be posted)',
                    inline: false
                });
                if (action === 'add' && tags.length > 0) {
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Added Tags`,
                        value: tags.map(tag => `#${tag}`).join(' '),
                        inline: false
                    });
                }
                else if (action === 'remove' && tags.length > 0) {
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Removed Tags`,
                        value: tags.map(tag => `#${tag}`).join(' '),
                        inline: false
                    });
                }
                await interaction.reply({ embeds: [embed] });
                break;
            }
            case 'post': {
                const tagFilter = interaction.options.getString('tag');
                const config = await (0, randomContentService_1.getOrCreateConfig)(guildId);
                if (!config.targetChannelId) {
                    throw new errorHandler_1.ValidationError('No target channel configured. Use `/randomcontent setup` first.');
                }
                const tags = tagFilter ? [tagFilter.toLowerCase()] : config.tagFilter;
                const success = await (0, randomContentService_1.postRandomContent)(interaction.client, guildId, config.targetChannelId, tags);
                if (!success) {
                    const embed = (0, embedBuilder_1.createEconomyEmbed)('No Content Available', tagFilter
                        ? `No content found with tag \`#${tagFilter}\``
                        : 'No content available for posting');
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                }
                else {
                    const embed = (0, embedBuilder_1.createSuccessEmbed)('Content Posted', `Random content has been posted to <#${config.targetChannelId}>`);
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                }
                break;
            }
            case 'list': {
                const tagFilter = interaction.options.getString('tag');
                const targetUser = interaction.options.getUser('user');
                const page = interaction.options.getInteger('page') || 1;
                // Non-admins can only view their own content
                const searchUserId = targetUser ? (isAdmin ? targetUser.id : userId) : userId;
                const result = await (0, contentSubmissionService_1.getUserContentSubmissions)(guildId, searchUserId, page, 10, tagFilter || undefined);
                const embed = (0, embedBuilder_1.createEconomyEmbed)(`Content Submissions${targetUser ? ` by ${targetUser.username}` : ''}`, result.submissions.length === 0 ? 'No content submissions found.' : undefined);
                if (result.submissions.length > 0) {
                    result.submissions.forEach((submission, index) => {
                        const formatted = (0, contentUtils_1.formatContentForDisplay)(submission);
                        const fieldValue = [
                            formatted.text ? `📝 ${formatted.text}` : '',
                            formatted.urls ? `🔗 ${formatted.urls}` : '',
                            formatted.attachments ? `📎 ${formatted.attachments}` : '',
                            formatted.tags ? `🏷️ ${formatted.tags}` : '',
                            `📊 Used ${submission.usageCount} times`
                        ].filter(Boolean).join('\n') || 'No content preview available';
                        embed.addFields({
                            name: `${index + 1}. ${(0, contentUtils_1.getContentTypeDescription)(submission.contentType)} (ID: ${submission._id})`,
                            value: fieldValue.substring(0, 1024),
                            inline: false
                        });
                    });
                    embed.setFooter({
                        text: `Page ${page} of ${Math.ceil(result.total / 10)} • Total: ${result.total} submissions`
                    });
                }
                await interaction.reply({ embeds: [embed] });
                break;
            }
            case 'delete': {
                const contentId = interaction.options.getString('content_id', true);
                try {
                    await (0, contentSubmissionService_1.deleteContentSubmission)(contentId, userId, guildId, isAdmin);
                    const embed = (0, embedBuilder_1.createSuccessEmbed)('Content Deleted', `Content submission \`${contentId}\` has been deleted successfully.`);
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                }
                catch (error) {
                    if (error instanceof errorHandler_1.ValidationError) {
                        throw error;
                    }
                    throw new errorHandler_1.ValidationError('Failed to delete content submission. Please check the content ID.');
                }
                break;
            }
            case 'stats': {
                const targetUser = interaction.options.getUser('user');
                const searchUserId = targetUser ? (isAdmin ? targetUser.id : undefined) : undefined;
                const stats = await (0, randomContentService_1.getContentStats)(guildId, searchUserId);
                const embed = (0, embedBuilder_1.createEconomyEmbed)(`Content Statistics${targetUser ? ` for ${targetUser.username}` : ''}`, 'Overview of content submissions and usage');
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Submission Stats`,
                    value: [
                        `📊 Total Submissions: ${stats.totalSubmissions}`,
                        `✅ Active Submissions: ${stats.activeSubmissions}`,
                        `🎯 Total Usage: ${stats.totalUsage}`,
                        ...(stats.userSubmissions !== undefined ? [`👤 User Submissions: ${stats.userSubmissions}`] : [])
                    ].join('\n'),
                    inline: false
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.TAG} Content by Type`,
                    value: Object.entries(stats.contentByType).length > 0
                        ? Object.entries(stats.contentByType)
                            .map(([type, count]) => `${(0, contentUtils_1.getContentTypeDescription)(type)}: ${count}`)
                            .join('\n')
                        : 'No content submissions',
                    inline: true
                }, {
                    name: `${embedBuilder_1.EMOJIS.MISC.TAG} Top Tags`,
                    value: stats.topTags.length > 0
                        ? stats.topTags.slice(0, 10)
                            .map(({ tag, count }) => `#${tag}: ${count}`)
                            .join('\n')
                        : 'No tags used',
                    inline: true
                });
                await interaction.reply({ embeds: [embed] });
                break;
            }
            default:
                throw new errorHandler_1.ValidationError('Unknown subcommand');
        }
    })
};
