"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MAX_TAG_LENGTH = exports.MAX_TAGS_PER_SUBMISSION = exports.MAX_URLS_PER_SUBMISSION = exports.MAX_ATTACHMENTS_PER_SUBMISSION = exports.MAX_ATTACHMENT_SIZE = exports.SUPPORTED_IMAGE_EXTENSIONS = void 0;
exports.isImageUrl = isImageUrl;
exports.isImageAttachment = isImageAttachment;
exports.validateUrl = validateUrl;
exports.validateDiscordCdnUrl = validateDiscordCdnUrl;
exports.extractHashtags = extractHashtags;
exports.removeHashtags = removeHashtags;
exports.extractUrls = extractUrls;
exports.cleanTextContent = cleanTextContent;
exports.validateAttachment = validateAttachment;
exports.hasValidContent = hasValidContent;
exports.getContentTypeDescription = getContentTypeDescription;
exports.formatContentForDisplay = formatContentForDisplay;
exports.canManageContent = canManageContent;
exports.sanitizeTag = sanitizeTag;
exports.validateTag = validateTag;
/**
 * Supported image file extensions
 */
exports.SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
/**
 * Maximum file size for attachments (8MB in bytes)
 */
exports.MAX_ATTACHMENT_SIZE = 8 * 1024 * 1024;
/**
 * Maximum number of attachments per submission
 */
exports.MAX_ATTACHMENTS_PER_SUBMISSION = 10;
/**
 * Maximum number of URLs per submission
 */
exports.MAX_URLS_PER_SUBMISSION = 10;
/**
 * Maximum number of tags per submission
 */
exports.MAX_TAGS_PER_SUBMISSION = 20;
/**
 * Maximum tag length
 */
exports.MAX_TAG_LENGTH = 50;
/**
 * Check if a URL points to an image
 */
function isImageUrl(url) {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname.toLowerCase();
        return exports.SUPPORTED_IMAGE_EXTENSIONS.some(ext => pathname.endsWith(ext));
    }
    catch {
        return false;
    }
}
/**
 * Check if an attachment is an image
 */
function isImageAttachment(attachment) {
    if (!attachment.contentType) {
        return isImageUrl(attachment.url);
    }
    return attachment.contentType.startsWith('image/');
}
/**
 * Validate URL format and accessibility
 */
function validateUrl(url) {
    try {
        const urlObj = new URL(url);
        // Only allow http and https protocols
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    }
    catch {
        return false;
    }
}
/**
 * Validate Discord CDN URL
 */
function validateDiscordCdnUrl(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname === 'cdn.discordapp.com' ||
            urlObj.hostname === 'media.discordapp.net';
    }
    catch {
        return false;
    }
}
/**
 * Extract and validate hashtags from text
 */
function extractHashtags(text) {
    if (!text)
        return [];
    const hashtagRegex = /#([a-zA-Z0-9_-]+)/g;
    const matches = text.match(hashtagRegex);
    if (!matches)
        return [];
    return matches
        .map(tag => tag.substring(1).toLowerCase()) // Remove # and convert to lowercase
        .filter(tag => tag.length > 0 && tag.length <= exports.MAX_TAG_LENGTH)
        .filter((tag, index, array) => array.indexOf(tag) === index) // Remove duplicates
        .slice(0, exports.MAX_TAGS_PER_SUBMISSION); // Limit number of tags
}
/**
 * Remove hashtags from text
 */
function removeHashtags(text) {
    if (!text)
        return '';
    return text.replace(/#[a-zA-Z0-9_-]+/g, '').trim();
}
/**
 * Extract URLs from text
 */
function extractUrls(text) {
    if (!text)
        return [];
    const urlRegex = /https?:\/\/[^\s]+/g;
    const matches = text.match(urlRegex);
    if (!matches)
        return [];
    return matches
        .filter(url => validateUrl(url))
        .filter((url, index, array) => array.indexOf(url) === index) // Remove duplicates
        .slice(0, exports.MAX_URLS_PER_SUBMISSION); // Limit number of URLs
}
/**
 * Clean text content by removing bot mentions and extra whitespace
 */
function cleanTextContent(text, botId) {
    if (!text)
        return '';
    let cleaned = text;
    // Remove bot mentions
    if (botId) {
        const botMentionRegex = new RegExp(`<@!?${botId}>`, 'g');
        cleaned = cleaned.replace(botMentionRegex, '');
    }
    else {
        // Remove all user mentions as fallback
        cleaned = cleaned.replace(/<@!?\d+>/g, '');
    }
    // Remove extra whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    return cleaned;
}
/**
 * Validate attachment size and type
 */
function validateAttachment(attachment) {
    // Check file size
    if (attachment.size > exports.MAX_ATTACHMENT_SIZE) {
        return {
            valid: false,
            reason: `File size (${(attachment.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (8MB)`
        };
    }
    // Check if it's a supported type (images or common file types)
    const supportedTypes = [
        'image/', 'text/', 'application/pdf', 'application/json',
        'video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav'
    ];
    if (attachment.contentType && !supportedTypes.some(type => attachment.contentType.startsWith(type))) {
        return {
            valid: false,
            reason: `File type (${attachment.contentType}) is not supported`
        };
    }
    return { valid: true };
}
/**
 * Check if message contains valid content for submission
 */
function hasValidContent(message, botId) {
    const cleanText = cleanTextContent(message.content, botId);
    const hasText = cleanText.length > 0;
    const hasAttachments = message.attachments.size > 0;
    const hasUrls = extractUrls(message.content).length > 0;
    return hasText || hasAttachments || hasUrls;
}
/**
 * Get content type description for display
 */
function getContentTypeDescription(contentType) {
    switch (contentType) {
        case 'text':
            return 'Text Content';
        case 'image':
            return 'Image Content';
        case 'link':
            return 'Link Content';
        case 'mixed':
            return 'Mixed Content';
        default:
            return 'Unknown Content';
    }
}
/**
 * Format content for display in embeds
 */
function formatContentForDisplay(content) {
    const result = {};
    if (content.contentText) {
        result.text = content.contentText.length > 100
            ? content.contentText.substring(0, 100) + '...'
            : content.contentText;
    }
    if (content.contentUrls && content.contentUrls.length > 0) {
        result.urls = content.contentUrls.slice(0, 3).join('\n');
        if (content.contentUrls.length > 3) {
            result.urls += `\n... and ${content.contentUrls.length - 3} more`;
        }
    }
    if (content.contentAttachments && content.contentAttachments.length > 0) {
        result.attachments = content.contentAttachments.slice(0, 3).join('\n');
        if (content.contentAttachments.length > 3) {
            result.attachments += `\n... and ${content.contentAttachments.length - 3} more`;
        }
    }
    if (content.tags && content.tags.length > 0) {
        result.tags = content.tags.map(tag => `#${tag}`).join(' ');
    }
    return result;
}
/**
 * Check if user has permission to manage content (admin or content owner)
 */
function canManageContent(userId, contentUserId, isAdmin) {
    return isAdmin || userId === contentUserId;
}
/**
 * Sanitize tag for storage
 */
function sanitizeTag(tag) {
    return tag
        .toLowerCase()
        .replace(/[^a-z0-9_-]/g, '')
        .substring(0, exports.MAX_TAG_LENGTH);
}
/**
 * Validate tag format
 */
function validateTag(tag) {
    return /^[a-zA-Z0-9_-]+$/.test(tag) && tag.length > 0 && tag.length <= exports.MAX_TAG_LENGTH;
}
